<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DestinationController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application.
|
*/

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/gallery', [HomeController::class, 'gallery'])->name('gallery');

// Redirect /home to dashboard for authenticated users
Route::get('/home', function () {
    return redirect('/dashboard');
})->middleware('auth');

// Dashboard route
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Authenticated routes
Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Destination routes
    Route::resource('destinations', DestinationController::class);
});

// Google OAuth routes (placeholder)
Route::get('/auth/google', function() {
    return redirect()->route('login')->with('info', 'Google OAuth will be available soon.');
});

Route::get('/auth/google/callback', function() {
    return redirect()->route('login')->with('info', 'Google OAuth will be available soon.');
});

// Include auth routes
require __DIR__.'/auth.php';
