<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900 dark:text-white leading-tight">
                    Edit Destination
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Update your destination details
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('destinations.show', $destination)); ?>" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                    </svg>
                    View
                </a>
                <a href="<?php echo e(route('destinations.index')); ?>" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form action="<?php echo e(route('destinations.update', $destination)); ?>" method="POST" enctype="multipart/form-data" x-data="imagePreview()">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Main Form -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Basic Information -->
                        <div class="card-glass p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Basic Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Nama Tempat -->
                                <div>
                                    <label for="nama_tempat" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Destination Name *
                                    </label>
                                    <input type="text" 
                                           id="nama_tempat" 
                                           name="nama_tempat" 
                                           value="<?php echo e(old('nama_tempat', $destination->nama_tempat)); ?>"
                                           placeholder="e.g., Bali, Tokyo, Paris"
                                           class="input-modern <?php $__errorArgs = ['nama_tempat'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           required>
                                    <?php $__errorArgs = ['nama_tempat'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Negara -->
                                <div>
                                    <label for="negara" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Country *
                                    </label>
                                    <input type="text" 
                                           id="negara" 
                                           name="negara" 
                                           value="<?php echo e(old('negara', $destination->negara)); ?>"
                                           placeholder="e.g., Indonesia, Japan, France"
                                           class="input-modern <?php $__errorArgs = ['negara'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           required>
                                    <?php $__errorArgs = ['negara'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Musim -->
                                <div>
                                    <label for="musim" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Best Season *
                                    </label>
                                    <select id="musim" 
                                            name="musim" 
                                            class="input-modern <?php $__errorArgs = ['musim'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            required>
                                        <option value="">Select Season</option>
                                        <?php $__currentLoopData = \App\Models\Destination::MUSIM_OPTIONS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $musim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($musim); ?>" <?php echo e(old('musim', $destination->musim) == $musim ? 'selected' : ''); ?>>
                                                <?php echo e(ucfirst($musim)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['musim'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Mood -->
                                <div>
                                    <label for="mood" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Travel Mood *
                                    </label>
                                    <select id="mood" 
                                            name="mood" 
                                            class="input-modern <?php $__errorArgs = ['mood'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            required>
                                        <option value="">Select Mood</option>
                                        <?php $__currentLoopData = \App\Models\Destination::MOOD_OPTIONS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mood): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($mood); ?>" <?php echo e(old('mood', $destination->mood) == $mood ? 'selected' : ''); ?>>
                                                <?php echo e(ucfirst($mood)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['mood'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                    Status *
                                </label>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <label class="relative flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 cursor-pointer hover:border-primary-300 dark:hover:border-primary-600 transition-colors">
                                        <input type="radio" 
                                               name="status" 
                                               value="0" 
                                               class="sr-only"
                                               <?php echo e(old('status', $destination->status) == '0' ? 'checked' : ''); ?>>
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 flex items-center justify-center">
                                                <div class="w-2 h-2 bg-purple-500 rounded-full hidden"></div>
                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-900 dark:text-white">Dream Destination</div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">I want to visit this place</div>
                                            </div>
                                        </div>
                                    </label>
                                    
                                    <label class="relative flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 cursor-pointer hover:border-primary-300 dark:hover:border-primary-600 transition-colors">
                                        <input type="radio" 
                                               name="status" 
                                               value="1" 
                                               class="sr-only"
                                               <?php echo e(old('status', $destination->status) == '1' ? 'checked' : ''); ?>>
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 flex items-center justify-center">
                                                <div class="w-2 h-2 bg-green-500 rounded-full hidden"></div>
                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-900 dark:text-white">Already Visited</div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">I've been to this place</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="card-glass p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Description</h3>
                            
                            <div>
                                <label for="deskripsi" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Tell us about this destination *
                                </label>
                                <textarea id="deskripsi" 
                                          name="deskripsi" 
                                          rows="6"
                                          placeholder="Describe what makes this destination special, what you love about it, or what you're looking forward to experiencing there..."
                                          class="input-modern <?php $__errorArgs = ['deskripsi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                          required><?php echo e(old('deskripsi', $destination->deskripsi)); ?></textarea>
                                <?php $__errorArgs = ['deskripsi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Minimum 10 characters</p>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex items-center justify-between">
                            <form action="<?php echo e(route('destinations.destroy', $destination)); ?>" 
                                  method="POST" 
                                  class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this destination? This action cannot be undone.')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn-secondary text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"/>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                    Delete Destination
                                </button>
                            </form>
                            
                            <div class="flex space-x-4">
                                <a href="<?php echo e(route('destinations.show', $destination)); ?>" class="btn-secondary">
                                    Cancel
                                </a>
                                <button type="submit" class="btn-primary">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                    Update Destination
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload Sidebar -->
                    <div class="lg:col-span-1">
                        <div class="card-glass p-6 sticky top-8">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Destination Image</h3>
                            
                            <!-- Current Image -->
                            <div class="mb-6">
                                <div class="aspect-w-16 aspect-h-12 rounded-xl overflow-hidden bg-gray-100 dark:bg-gray-700">
                                    <div x-show="!imageUrl" class="relative">
                                        <img src="<?php echo e($destination->image_url); ?>" 
                                             alt="<?php echo e($destination->nama_tempat); ?>"
                                             class="w-full h-48 object-cover">
                                        <div class="absolute top-2 right-2">
                                            <span class="px-2 py-1 text-xs font-medium bg-black/50 text-white rounded-full">
                                                Current
                                            </span>
                                        </div>
                                    </div>
                                    <img x-show="imageUrl" 
                                         :src="imageUrl" 
                                         alt="Preview" 
                                         class="w-full h-48 object-cover">
                                </div>
                            </div>

                            <!-- Upload Button -->
                            <div class="mb-4">
                                <label for="gambar" class="block w-full">
                                    <input type="file" 
                                           id="gambar" 
                                           name="gambar" 
                                           accept="image/*"
                                           @change="previewImage"
                                           class="sr-only">
                                    <div class="w-full btn-secondary text-center cursor-pointer">
                                        <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                                        </svg>
                                        <span x-show="!imageUrl">Change Image</span>
                                        <span x-show="imageUrl">Choose Different Image</span>
                                    </div>
                                </label>
                                <?php $__errorArgs = ['gambar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Remove New Image Button -->
                            <button type="button" 
                                    x-show="imageUrl"
                                    @click="removeImage()"
                                    class="w-full btn-secondary text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 mb-4">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                                Cancel New Image
                            </button>

                            <!-- Image Guidelines -->
                            <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">Image Guidelines</h4>
                                <ul class="text-xs text-blue-700 dark:text-blue-400 space-y-1">
                                    <li>• Maximum file size: 2MB</li>
                                    <li>• Supported formats: JPG, PNG, GIF</li>
                                    <li>• Recommended ratio: 16:9</li>
                                    <li>• High quality images work best</li>
                                </ul>
                            </div>

                            <!-- Current Image Info -->
                            <?php if($destination->gambar): ?>
                            <div class="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <h4 class="text-sm font-medium text-green-900 dark:text-green-300 mb-2">Current Image</h4>
                                <p class="text-xs text-green-700 dark:text-green-400">
                                    You have uploaded a custom image. If you don't select a new image, the current one will be kept.
                                </p>
                            </div>
                            <?php else: ?>
                            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <h4 class="text-sm font-medium text-purple-900 dark:text-purple-300 mb-2">Auto Image</h4>
                                <p class="text-xs text-purple-700 dark:text-purple-400">
                                    Currently using an automatic image from Unsplash. Upload your own image to replace it.
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Radio button styling
        document.querySelectorAll('input[name="status"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Reset all radio buttons
                document.querySelectorAll('input[name="status"]').forEach(r => {
                    const label = r.closest('label');
                    const dot = label.querySelector('div > div');
                    label.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
                    label.classList.add('border-gray-200', 'dark:border-gray-600');
                    dot.classList.add('hidden');
                });
                
                // Style selected radio button
                if (this.checked) {
                    const label = this.closest('label');
                    const dot = label.querySelector('div > div');
                    label.classList.remove('border-gray-200', 'dark:border-gray-600');
                    label.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
                    dot.classList.remove('hidden');
                }
            });
        });

        // Trigger initial styling
        document.querySelector('input[name="status"]:checked')?.dispatchEvent(new Event('change'));
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\project_uas_abduls\resources\views/destinations/edit.blade.php ENDPATH**/ ?>