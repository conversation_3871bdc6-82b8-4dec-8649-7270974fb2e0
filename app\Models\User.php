<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // Relationships
    public function destinations()
    {
        return $this->hasMany(Destination::class);
    }

    // Accessors
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return $this->avatar;
        }

        // Default avatar using Gravatar
        $hash = md5(strtolower(trim($this->email)));
        return "https://www.gravatar.com/avatar/{$hash}?d=identicon&s=200";
    }

    // Helper methods for statistics
    public function getTotalDestinationsAttribute(): int
    {
        return $this->destinations()->count();
    }

    public function getVisitedDestinationsAttribute(): int
    {
        return $this->destinations()->where('status', true)->count();
    }

    public function getDreamDestinationsAttribute(): int
    {
        return $this->destinations()->where('status', false)->count();
    }

    public function getFavoriteMusimAttribute(): ?string
    {
        return $this->destinations()
            ->selectRaw('musim, COUNT(*) as count')
            ->groupBy('musim')
            ->orderByDesc('count')
            ->first()?->musim;
    }
}
