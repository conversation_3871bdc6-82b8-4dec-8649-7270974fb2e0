<?php

// Download alternative images for failed downloads
$alternativeDestinations = [
    [
        'name' => 'antelope-canyon-usa',
        'url' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'antelope-canyon-usa.jpg',
        'description' => 'Antelope Canyon light beams slot canyon'
    ],
    [
        'name' => 'meteora-greece',
        'url' => 'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'meteora-greece.jpg',
        'description' => 'Meteora monasteries on rock formations'
    ],
    [
        'name' => 'hallstatt-austria',
        'url' => 'https://images.unsplash.com/photo-1587595431973-160d0d94add1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'hallstatt-austria.jpg',
        'description' => 'Hallstatt lakeside village Austria Alps'
    ],
    [
        'name' => 'geirangerfjord-norway',
        'url' => 'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'geirangerfjord-norway.jpg',
        'description' => 'Geirangerfjord waterfalls Norwegian fjord'
    ],
    [
        'name' => 'dolomites-italy',
        'url' => 'https://images.unsplash.com/photo-1523906834658-6e24ef2386f9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'dolomites-italy.jpg',
        'description' => 'Dolomites limestone peaks Italian Alps'
    ]
];

$downloadDir = 'public/images/destinations/';

echo "Downloading alternative destination images...\n";

foreach ($alternativeDestinations as $destination) {
    $filePath = $downloadDir . $destination['filename'];
    
    if (file_exists($filePath)) {
        echo "Skipping {$destination['description']} (already exists)\n";
        continue;
    }
    
    echo "Downloading {$destination['description']}... ";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $destination['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $imageData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($imageData !== false && $httpCode == 200) {
        file_put_contents($filePath, $imageData);
        $fileSize = filesize($filePath);
        echo "✓ Downloaded ({$destination['filename']}) - " . round($fileSize/1024) . "KB\n";
    } else {
        echo "✗ Failed to download (HTTP: $httpCode)\n";
    }
    
    sleep(1);
}

echo "\nDownload completed!\n";

?>
