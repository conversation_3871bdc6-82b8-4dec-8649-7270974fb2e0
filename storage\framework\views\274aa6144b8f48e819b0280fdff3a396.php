<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" x-data="darkMode()" :class="{ 'dark': dark }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo e(__('messages.gallery')); ?> - Dream Destinations</title>
    <meta name="description" content="<?php echo e(app()->getLocale() == 'id' ? 'Jelajahi destinasi menakjubkan dari komunitas kami. Dapatkan inspirasi untuk petualangan berikutnya dengan Dream Destinations.' : 'Explore amazing destinations from our community. Get inspired for your next adventure with Dream Destinations.'); ?>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Navigation -->
    <nav class="glass-card sticky top-0 z-40 border-b border-white/20 dark:border-gray-700/20" data-aos="fade-down">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16 md:h-20">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-2 hover:scale-105 transition-transform duration-300">
                        <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center shadow-lg">
                            <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <span class="text-lg md:text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            <span class="hidden sm:inline">Dream Destinations</span>
                            <span class="sm:hidden">Dream</span>
                        </span>
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="flex items-center space-x-2 md:space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center space-x-1 md:space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                            <svg class="w-4 h-4 md:w-5 md:h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-xs md:text-sm text-gray-600 dark:text-gray-400"><?php echo e(app()->getLocale() == 'id' ? 'ID' : 'EN'); ?></span>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95" class="absolute right-0 mt-2 w-32 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-50">
                            <a href="<?php echo e(route('language.switch', 'en')); ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg transition-colors duration-200">
                                <span class="mr-2">🇺🇸</span> <?php echo e(__('messages.english')); ?>

                            </a>
                            <a href="<?php echo e(route('language.switch', 'id')); ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-b-lg transition-colors duration-200">
                                <span class="mr-2">🇮🇩</span> <?php echo e(__('messages.indonesian')); ?>

                            </a>
                        </div>
                    </div>

                    <!-- Dark Mode Toggle -->
                    <button @click="toggle()" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                        <svg x-show="!dark" class="w-4 h-4 md:w-5 md:h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"/>
                        </svg>
                        <svg x-show="dark" class="w-4 h-4 md:w-5 md:h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
                        </svg>
                    </button>

                    <!-- Desktop Navigation Links -->
                    <div class="hidden md:flex items-center space-x-4">
                        <a href="<?php echo e(route('home')); ?>" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 font-medium transition-all duration-300 hover:scale-105">
                            <?php echo e(__('messages.home')); ?>

                        </a>

                        <?php if(auth()->guard()->check()): ?>
                            <!-- User Profile Dropdown -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300">
                                    <?php if(auth()->user()->avatar): ?>
                                        <img src="<?php echo e(auth()->user()->avatar); ?>" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                    <?php else: ?>
                                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-sm font-medium"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?php echo e(auth()->user()->name); ?></span>
                                </button>

                                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-50">
                                    <a href="<?php echo e(route('dashboard')); ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                        </svg>
                                        <?php echo e(__('messages.dashboard')); ?>

                                    </a>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-b-lg">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                                            </svg>
                                            <?php echo e(__('messages.logout')); ?>

                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo e(route('login')); ?>" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 font-medium transition-all duration-300 hover:scale-105">
                                <?php echo e(__('messages.login')); ?>

                            </a>
                            <a href="<?php echo e(route('register')); ?>" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg"><?php echo e(__('messages.get_started')); ?></a>
                        <?php endif; ?>
                    </div>

                    <!-- Mobile Menu Button -->
                    <div class="md:hidden" x-data="{ open: false }">
                        <button @click="open = !open" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300">
                            <svg x-show="!open" class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                            <svg x-show="open" class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>

                        <!-- Mobile Menu -->
                        <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95" class="absolute right-4 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-50">
                            <div class="py-2">
                                <a href="<?php echo e(route('home')); ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <?php echo e(__('messages.home')); ?>

                                </a>
                                <?php if(auth()->guard()->check()): ?>
                                    <a href="<?php echo e(route('dashboard')); ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <?php echo e(__('messages.dashboard')); ?>

                                    </a>
                                    <div class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-t border-gray-200 dark:border-gray-600">
                                        <?php if(auth()->user()->avatar): ?>
                                            <img src="<?php echo e(auth()->user()->avatar); ?>" alt="Profile" class="w-6 h-6 rounded-full object-cover mr-2">
                                        <?php else: ?>
                                            <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-2">
                                                <span class="text-white text-xs font-medium"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <span class="truncate"><?php echo e(auth()->user()->name); ?></span>
                                    </div>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                            <?php echo e(__('messages.logout')); ?>

                                        </button>
                                    </form>
                                <?php else: ?>
                                    <a href="<?php echo e(route('login')); ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <?php echo e(__('messages.login')); ?>

                                    </a>
                                    <a href="<?php echo e(route('register')); ?>" class="block px-4 py-2 text-sm bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 transition-colors duration-200 rounded-b-lg">
                                        <?php echo e(__('messages.get_started')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="py-16 bg-gradient-to-r from-primary-600 to-secondary-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4" data-aos="fade-up">
                <?php echo e(app()->getLocale() == 'id' ? 'Galeri Destinasi' : 'Destination Gallery'); ?>

            </h1>
            <p class="text-xl text-white/90 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                <?php echo e(app()->getLocale() == 'id' ? 'Jelajahi destinasi menakjubkan dari komunitas kami dan dapatkan inspirasi untuk petualangan berikutnya' : 'Explore amazing destinations from our community and get inspired for your next adventure'); ?>

            </p>
        </div>
    </section>

    <!-- Filters -->
    <section class="py-8 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <input type="text"
                           name="search"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="<?php echo e(__('messages.search_destinations')); ?>"
                           class="input-modern">
                </div>

                <!-- Season Filter -->
                <div>
                    <select name="musim" class="input-modern">
                        <option value=""><?php echo e(__('messages.all_seasons')); ?></option>
                        <?php $__currentLoopData = \App\Models\Destination::MUSIM_OPTIONS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $musim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($musim); ?>" <?php echo e(request('musim') == $musim ? 'selected' : ''); ?>>
                                <?php echo e(__('messages.' . $musim)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Mood Filter -->
                <div>
                    <select name="mood" class="input-modern">
                        <option value=""><?php echo e(__('messages.all_moods')); ?></option>
                        <?php $__currentLoopData = \App\Models\Destination::MOOD_OPTIONS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mood): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($mood); ?>" <?php echo e(request('mood') == $mood ? 'selected' : ''); ?>>
                                <?php echo e(__('messages.' . $mood)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <select name="status" class="input-modern">
                        <option value=""><?php echo e(__('messages.all_status')); ?></option>
                        <option value="dream" <?php echo e(request('status') == 'dream' ? 'selected' : ''); ?>><?php echo e(__('messages.dream')); ?></option>
                        <option value="visited" <?php echo e(request('status') == 'visited' ? 'selected' : ''); ?>><?php echo e(__('messages.visited')); ?></option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="md:col-span-4 flex justify-center">
                    <button type="submit" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                        <?php echo e(app()->getLocale() == 'id' ? 'Terapkan Filter' : 'Apply Filters'); ?>

                    </button>
                    <?php if(request()->hasAny(['search', 'musim', 'mood', 'status'])): ?>
                        <a href="<?php echo e(route('gallery')); ?>" class="btn-secondary ml-3">
                            <?php echo e(__('messages.clear_filters')); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <?php if($destinations->count() > 0): ?>
                <!-- Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover-lift hover-glow transition-smooth animate-scale-in" style="animation-delay: <?php echo e($loop->index * 0.1); ?>s;">
                        <div class="relative aspect-ratio-3-2 overflow-hidden">
                            <img src="<?php echo e($destination->image_url); ?>"
                                 alt="<?php echo e($destination->nama_tempat); ?>"
                                 class="w-full h-full destination-image image-reveal"
                                 loading="lazy"
                                 onload="this.classList.add('loaded')">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-4 left-4 right-4">
                                    <h3 class="text-white font-bold text-lg mb-1"><?php echo e($destination->nama_tempat); ?></h3>
                                    <p class="text-white/90 text-sm"><?php echo e($destination->negara); ?></p>
                                </div>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full <?php echo e($destination->status_badge_class); ?>">
                                    <?php echo e($destination->status_text); ?>

                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-900 dark:text-white mb-1"><?php echo e($destination->nama_tempat); ?></h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2"><?php echo e($destination->negara); ?></p>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mb-3 line-clamp-2">
                                <?php echo e(Str::limit($destination->deskripsi, 80)); ?>

                            </p>
                            
                            <div class="flex items-center justify-between text-xs mb-3">
                                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded-full">
                                    <?php echo e(ucfirst($destination->musim)); ?>

                                </span>
                                <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 rounded-full">
                                    <?php echo e(ucfirst($destination->mood)); ?>

                                </span>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <img class="w-5 h-5 rounded-full mr-2" src="<?php echo e($destination->user->avatar_url); ?>" alt="<?php echo e($destination->user->name); ?>">
                                    <span><?php echo e($destination->user->name); ?></span>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo e($destination->created_at->diffForHumans()); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    <?php echo e($destinations->links()); ?>

                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">No destinations found</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        <?php if(request()->hasAny(['search', 'musim', 'mood', 'status'])): ?>
                            Try adjusting your search criteria or filters to find what you're looking for.
                        <?php else: ?>
                            Be the first to share your dream destination with the community!
                        <?php endif; ?>
                    </p>
                    <?php if(request()->hasAny(['search', 'musim', 'mood', 'status'])): ?>
                        <a href="<?php echo e(route('gallery')); ?>" class="btn-secondary mr-3">
                            Clear Filters
                        </a>
                    <?php endif; ?>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('destinations.create')); ?>" class="btn-primary">
                            Add Your Destination
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('register')); ?>" class="btn-primary">
                            Join Community
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- CTA Section -->
    <?php if(auth()->guard()->guest()): ?>
    <section class="py-16 bg-gradient-to-r from-primary-600 to-secondary-600">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-white mb-4" data-aos="fade-up">
                Ready to Share Your Dreams?
            </h2>
            <p class="text-lg text-white/90 mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                Join our community and start building your own collection of dream destinations.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="200">
                <a href="<?php echo e(route('register')); ?>" class="bg-white text-primary-600 font-bold px-8 py-3 rounded-xl hover:bg-gray-100 transition-colors">
                    Get Started Free
                </a>
                <a href="<?php echo e(route('login')); ?>" class="border-2 border-white text-white font-bold px-8 py-3 rounded-xl hover:bg-white hover:text-primary-600 transition-colors">
                    Sign In
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-2 mb-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold">Dream Destinations</span>
                </div>
                <p class="text-gray-400 mb-6">Explore, dream, and discover amazing destinations</p>
                <div class="border-t border-gray-800 pt-6">
                    <p class="text-gray-500 text-sm">
                        © <?php echo e(date('Y')); ?> Dream Destinations. Dibuat dengan ❤️ oleh Abdul Somad Maulana (241351076) - Fakultas Informatika
                    </p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Herd\project_uas_abduls\resources\views/gallery.blade.php ENDPATH**/ ?>