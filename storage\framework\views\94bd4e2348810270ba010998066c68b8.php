<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" x-data="darkMode()" :class="{ 'dark': dark }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Gallery - Dream Destinations</title>
    <meta name="description" content="Explore amazing destinations from our community. Get inspired for your next adventure with Dream Destinations.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Navigation -->
    <nav class="glass-card sticky top-0 z-40 border-b border-white/20 dark:border-gray-700/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <span class="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                            Dream Destinations
                        </span>
                    </a>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button @click="toggle()" class="p-2 rounded-lg glass-button hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-300">
                        <svg x-show="!dark" class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"/>
                        </svg>
                        <svg x-show="dark" class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
                        </svg>
                    </button>

                    <!-- Navigation Links -->
                    <a href="<?php echo e(route('home')); ?>" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors">
                        Home
                    </a>

                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="btn-primary text-sm">Dashboard</a>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors">
                            Login
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="btn-primary text-sm">Get Started</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="py-16 bg-gradient-to-r from-primary-600 to-secondary-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4" data-aos="fade-up">
                Destination Gallery
            </h1>
            <p class="text-xl text-white/90 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                Explore amazing destinations from our community and get inspired for your next adventure
            </p>
        </div>
    </section>

    <!-- Filters -->
    <section class="py-8 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search destinations..."
                           class="input-modern">
                </div>

                <!-- Season Filter -->
                <div>
                    <select name="musim" class="input-modern">
                        <option value="">All Seasons</option>
                        <?php $__currentLoopData = \App\Models\Destination::MUSIM_OPTIONS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $musim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($musim); ?>" <?php echo e(request('musim') == $musim ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst($musim)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Mood Filter -->
                <div>
                    <select name="mood" class="input-modern">
                        <option value="">All Moods</option>
                        <?php $__currentLoopData = \App\Models\Destination::MOOD_OPTIONS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mood): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($mood); ?>" <?php echo e(request('mood') == $mood ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst($mood)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <select name="status" class="input-modern">
                        <option value="">All Status</option>
                        <option value="dream" <?php echo e(request('status') == 'dream' ? 'selected' : ''); ?>>Dreams</option>
                        <option value="visited" <?php echo e(request('status') == 'visited' ? 'selected' : ''); ?>>Visited</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="md:col-span-4 flex justify-center">
                    <button type="submit" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                        Apply Filters
                    </button>
                    <?php if(request()->hasAny(['search', 'musim', 'mood', 'status'])): ?>
                        <a href="<?php echo e(route('gallery')); ?>" class="btn-secondary ml-3">
                            Clear Filters
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <?php if($destinations->count() > 0): ?>
                <!-- Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <img src="<?php echo e($destination->image_url); ?>"
                                 alt="<?php echo e($destination->nama_tempat); ?>"
                                 class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105"
                                 loading="lazy">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-4 left-4 right-4">
                                    <h3 class="text-white font-bold text-lg mb-1"><?php echo e($destination->nama_tempat); ?></h3>
                                    <p class="text-white/90 text-sm"><?php echo e($destination->negara); ?></p>
                                </div>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full <?php echo e($destination->status_badge_class); ?>">
                                    <?php echo e($destination->status_text); ?>

                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-900 dark:text-white mb-1"><?php echo e($destination->nama_tempat); ?></h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2"><?php echo e($destination->negara); ?></p>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mb-3 line-clamp-2">
                                <?php echo e(Str::limit($destination->deskripsi, 80)); ?>

                            </p>
                            
                            <div class="flex items-center justify-between text-xs mb-3">
                                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded-full">
                                    <?php echo e(ucfirst($destination->musim)); ?>

                                </span>
                                <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 rounded-full">
                                    <?php echo e(ucfirst($destination->mood)); ?>

                                </span>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <img class="w-5 h-5 rounded-full mr-2" src="<?php echo e($destination->user->avatar_url); ?>" alt="<?php echo e($destination->user->name); ?>">
                                    <span><?php echo e($destination->user->name); ?></span>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo e($destination->created_at->diffForHumans()); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    <?php echo e($destinations->links()); ?>

                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">No destinations found</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        <?php if(request()->hasAny(['search', 'musim', 'mood', 'status'])): ?>
                            Try adjusting your search criteria or filters to find what you're looking for.
                        <?php else: ?>
                            Be the first to share your dream destination with the community!
                        <?php endif; ?>
                    </p>
                    <?php if(request()->hasAny(['search', 'musim', 'mood', 'status'])): ?>
                        <a href="<?php echo e(route('gallery')); ?>" class="btn-secondary mr-3">
                            Clear Filters
                        </a>
                    <?php endif; ?>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('destinations.create')); ?>" class="btn-primary">
                            Add Your Destination
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('register')); ?>" class="btn-primary">
                            Join Community
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- CTA Section -->
    <?php if(auth()->guard()->guest()): ?>
    <section class="py-16 bg-gradient-to-r from-primary-600 to-secondary-600">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-white mb-4" data-aos="fade-up">
                Ready to Share Your Dreams?
            </h2>
            <p class="text-lg text-white/90 mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                Join our community and start building your own collection of dream destinations.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="200">
                <a href="<?php echo e(route('register')); ?>" class="bg-white text-primary-600 font-bold px-8 py-3 rounded-xl hover:bg-gray-100 transition-colors">
                    Get Started Free
                </a>
                <a href="<?php echo e(route('login')); ?>" class="border-2 border-white text-white font-bold px-8 py-3 rounded-xl hover:bg-white hover:text-primary-600 transition-colors">
                    Sign In
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-2 mb-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold">Dream Destinations</span>
                </div>
                <p class="text-gray-400 mb-6">Explore, dream, and discover amazing destinations</p>
                <div class="border-t border-gray-800 pt-6">
                    <p class="text-gray-500 text-sm">
                        © <?php echo e(date('Y')); ?> Dream Destinations. Dibuat dengan ❤️ oleh Abdul Somad Maulana (241351076) - Fakultas Informatika
                    </p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Herd\project_uas_abduls\resources\views\gallery.blade.php ENDPATH**/ ?>