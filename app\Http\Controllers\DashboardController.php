<?php

namespace App\Http\Controllers;

use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Get user statistics
        $totalDestinations = $user->destinations()->count() ?: 0;
        $visitedDestinations = $user->destinations()->where('status', true)->count() ?: 0;
        $dreamDestinations = $user->destinations()->where('status', false)->count() ?: 0;

        // Get statistics by musim
        $musimStats = $user->destinations()
            ->select('musim', DB::raw('count(*) as count'))
            ->groupBy('musim')
            ->pluck('count', 'musim')
            ->toArray();

        // Get statistics by mood
        $moodStats = $user->destinations()
            ->select('mood', DB::raw('count(*) as count'))
            ->groupBy('mood')
            ->pluck('count', 'mood')
            ->toArray();

        // Get recent destinations
        $recentDestinations = $user->destinations()
            ->latest()
            ->take(6)
            ->get();

        // Get favorite musim
        $favoriteMusim = $user->destinations()
            ->select('musim', DB::raw('count(*) as count'))
            ->groupBy('musim')
            ->orderByDesc('count')
            ->first()?->musim;

        // Get favorite mood
        $favoriteMood = $user->destinations()
            ->select('mood', DB::raw('count(*) as count'))
            ->groupBy('mood')
            ->orderByDesc('count')
            ->first()?->mood;

        return view('dashboard', compact(
            'totalDestinations',
            'visitedDestinations',
            'dreamDestinations',
            'musimStats',
            'moodStats',
            'recentDestinations',
            'favoriteMusim',
            'favoriteMood'
        ));
    }
}
