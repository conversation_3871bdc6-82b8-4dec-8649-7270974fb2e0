<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Destination;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Destination>
 */
class DestinationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $destinations = [
            ['nama' => 'Bali', 'negara' => 'Indonesia'],
            ['nama' => 'Tokyo', 'negara' => 'Japan'],
            ['nama' => 'Paris', 'negara' => 'France'],
            ['nama' => 'Santorini', 'negara' => 'Greece'],
            ['nama' => 'Maldives', 'negara' => 'Maldives'],
            ['nama' => 'Iceland', 'negara' => 'Iceland'],
            ['nama' => 'New York', 'negara' => 'United States'],
            ['nama' => 'Dubai', 'negara' => 'UAE'],
            ['nama' => 'Switzerland', 'negara' => 'Switzerland'],
            ['nama' => 'Thailand', 'negara' => 'Thailand'],
            ['nama' => 'Norway', 'negara' => 'Norway'],
            ['nama' => 'Australia', 'negara' => 'Australia'],
            ['nama' => 'Morocco', 'negara' => 'Morocco'],
            ['nama' => 'Peru', 'negara' => 'Peru'],
            ['nama' => 'South Korea', 'negara' => 'South Korea'],
        ];

        $destination = $this->faker->randomElement($destinations);

        return [
            'user_id' => User::factory(),
            'nama_tempat' => $destination['nama'],
            'negara' => $destination['negara'],
            'musim' => $this->faker->randomElement(Destination::MUSIM_OPTIONS),
            'mood' => $this->faker->randomElement(Destination::MOOD_OPTIONS),
            'status' => $this->faker->boolean(30), // 30% chance of being visited
            'deskripsi' => $this->faker->paragraphs(3, true),
            'gambar' => null, // Will use Unsplash API
        ];
    }

    /**
     * Indicate that the destination has been visited.
     */
    public function visited(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => true,
        ]);
    }

    /**
     * Indicate that the destination is still a dream.
     */
    public function dream(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }
}
