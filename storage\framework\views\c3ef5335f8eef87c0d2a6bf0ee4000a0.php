<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900 dark:text-white leading-tight">
                    Profile Settings
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Manage your account information and preferences
                </p>
            </div>
            <a href="<?php echo e(route('dashboard')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                </svg>
                Back to Dashboard
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Overview -->
                <div class="lg:col-span-1">
                    <div class="card-glass p-6 text-center" data-aos="fade-up">
                        <div class="mb-6">
                            <img class="w-24 h-24 rounded-full mx-auto object-cover border-4 border-white dark:border-gray-700 shadow-lg"
                                 src="<?php echo e(Auth::user()->avatar_url); ?>"
                                 alt="<?php echo e(Auth::user()->name); ?>">
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2"><?php echo e(Auth::user()->name); ?></h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4"><?php echo e(Auth::user()->email); ?></p>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Member since <?php echo e(Auth::user()->created_at->format('M Y')); ?>

                        </div>

                        <!-- Stats -->
                        <div class="mt-6 grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo e(Auth::user()->destinations()->count()); ?></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Total Destinations</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo e(Auth::user()->destinations()->where('status', true)->count()); ?></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Visited</div>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Profile Forms -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Profile Information -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Profile Information</h3>
                        <?php echo $__env->make('profile.partials.update-profile-information-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- Password Update -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Update Password</h3>
                        <?php echo $__env->make('profile.partials.update-password-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- Account Actions -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Account Actions</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Export Data -->
                            <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-2">Export Your Data</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Download all your destinations and data.</p>
                                <button class="btn-secondary text-sm">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                    Export Data
                                </button>
                            </div>

                            <!-- Privacy Settings -->
                            <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-2">Privacy Settings</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Control who can see your destinations.</p>
                                <div class="flex items-center">
                                    <input type="checkbox" id="public_profile" class="rounded border-gray-300 text-primary-600 shadow-sm focus:ring-primary-500" checked>
                                    <label for="public_profile" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Show in public gallery</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Account -->
                    <div class="card-glass p-6 border-red-200 dark:border-red-800" data-aos="fade-up" data-aos-delay="400">
                        <h3 class="text-lg font-bold text-red-900 dark:text-red-300 mb-6">Danger Zone</h3>
                        <?php echo $__env->make('profile.partials.delete-user-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\project_uas_abduls\resources\views/profile/edit.blade.php ENDPATH**/ ?>