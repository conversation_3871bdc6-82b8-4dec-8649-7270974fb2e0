<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Destination;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DestinationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some dummy users first
        $users = User::factory(5)->create();

        // Get admin user
        $admin = User::where('email', '<EMAIL>')->first();
        if ($admin) {
            $users->push($admin);
        }

        // Create destinations for each user
        foreach ($users as $user) {
            // Create 3-8 destinations per user
            $destinationCount = rand(3, 8);

            for ($i = 0; $i < $destinationCount; $i++) {
                Destination::factory()->create([
                    'user_id' => $user->id,
                ]);
            }
        }

        $this->command->info('Created destinations for ' . $users->count() . ' users');
    }
}
