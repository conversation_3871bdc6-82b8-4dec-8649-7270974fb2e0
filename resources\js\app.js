import './bootstrap';

import Alpine from 'alpinejs';
import AOS from 'aos';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Chart from 'chart.js/auto';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Make libraries available globally
window.Alpine = Alpine;
window.AOS = AOS;
window.gsap = gsap;
window.ScrollTrigger = ScrollTrigger;
window.Chart = Chart;

// Alpine.js data components
Alpine.data('darkMode', () => ({
    dark: localStorage.getItem('darkMode') === 'true' ||
          (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches),

    init() {
        this.updateTheme();
    },

    toggle() {
        this.dark = !this.dark;
        this.updateTheme();
        localStorage.setItem('darkMode', this.dark);
    },

    updateTheme() {
        if (this.dark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }
}));

Alpine.data('imagePreview', () => ({
    imageUrl: null,

    previewImage(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.imageUrl = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    },

    removeImage() {
        this.imageUrl = null;
        // Reset file input
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) fileInput.value = '';
    }
}));

Alpine.data('searchFilter', () => ({
    search: '',
    musim: '',
    mood: '',
    status: '',

    get filteredItems() {
        // This will be implemented in the blade templates
        return [];
    },

    clearFilters() {
        this.search = '';
        this.musim = '';
        this.mood = '';
        this.status = '';
    }
}));

Alpine.data('flashMessage', () => ({
    show: false,
    message: '',
    type: 'success',

    init() {
        // Check for flash messages from Laravel session
        const flashData = document.querySelector('[data-flash]');
        if (flashData) {
            const data = JSON.parse(flashData.dataset.flash);
            this.showMessage(data.message, data.type);
        }
    },

    showMessage(message, type = 'success') {
        this.message = message;
        this.type = type;
        this.show = true;

        // Auto hide after 5 seconds
        setTimeout(() => {
            this.hide();
        }, 5000);
    },

    hide() {
        this.show = false;
        setTimeout(() => {
            this.message = '';
        }, 300);
    }
}));

// Initialize AOS when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // Initialize GSAP animations
    initGSAPAnimations();
});

function initGSAPAnimations() {
    // Hero section animations
    gsap.from('.hero-title', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    });

    gsap.from('.hero-subtitle', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.3,
        ease: 'power3.out'
    });

    gsap.from('.hero-cta', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.6,
        ease: 'power3.out'
    });

    // Card animations on scroll
    gsap.utils.toArray('.card-animate').forEach((card, index) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            duration: 0.8,
            y: 50,
            opacity: 0,
            delay: index * 0.1,
            ease: 'power3.out'
        });
    });

    // Parallax effect for hero background
    gsap.utils.toArray('.parallax').forEach(element => {
        gsap.to(element, {
            yPercent: -50,
            ease: 'none',
            scrollTrigger: {
                trigger: element,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    });
}

// Utility functions
window.utils = {
    // Format date
    formatDate(date) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },

    // Debounce function for search
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Copy to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('Failed to copy: ', err);
            return false;
        }
    }
};

Alpine.start();
