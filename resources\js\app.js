import './bootstrap';

import Alpine from 'alpinejs';

// Utility functions
window.utils = {
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    animateValue: function(obj, start, end, duration) {
        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            obj.innerHTML = Math.floor(progress * (end - start) + start);
            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        window.requestAnimationFrame(step);
    }
};
import AOS from 'aos';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Chart from 'chart.js/auto';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Make libraries available globally
window.Alpine = Alpine;
window.AOS = AOS;
window.gsap = gsap;
window.ScrollTrigger = ScrollTrigger;
window.Chart = Chart;

// Alpine.js data components
Alpine.data('darkMode', () => ({
    dark: localStorage.getItem('darkMode') === 'true' ||
          (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches),

    init() {
        this.updateTheme();
    },

    toggle() {
        this.dark = !this.dark;
        this.updateTheme();
        localStorage.setItem('darkMode', this.dark);
    },

    updateTheme() {
        if (this.dark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }
}));

Alpine.data('imagePreview', () => ({
    imageUrl: null,

    previewImage(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.imageUrl = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    },

    removeImage() {
        this.imageUrl = null;
        // Reset file input
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) fileInput.value = '';
    }
}));

Alpine.data('searchFilter', () => ({
    search: '',
    musim: '',
    mood: '',
    status: '',

    get filteredItems() {
        // This will be implemented in the blade templates
        return [];
    },

    clearFilters() {
        this.search = '';
        this.musim = '';
        this.mood = '';
        this.status = '';
    }
}));

// Destination Filter Component
Alpine.data('destinationFilter', () => ({
    searchTerm: '',
    selectedSeason: '',
    selectedMood: '',
    selectedStatus: '',

    init() {
        // Initialize from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        this.searchTerm = urlParams.get('search') || '';
        this.selectedSeason = urlParams.get('musim') || '';
        this.selectedMood = urlParams.get('mood') || '';
        this.selectedStatus = urlParams.get('status') || '';

        // Setup debounced navigation
        this.setupDebounce();
    },

    filterDestinations() {
        const params = new URLSearchParams();

        if (this.searchTerm.trim()) params.append('search', this.searchTerm.trim());
        if (this.selectedSeason) params.append('musim', this.selectedSeason);
        if (this.selectedMood) params.append('mood', this.selectedMood);
        if (this.selectedStatus) params.append('status', this.selectedStatus);

        // Preserve current sort
        const currentSort = new URLSearchParams(window.location.search).get('sort') || 'created_at';
        const currentOrder = new URLSearchParams(window.location.search).get('order') || 'desc';
        params.append('sort', currentSort);
        params.append('order', currentOrder);

        // Use debounced navigation
        this.debouncedNavigate(params);
    },

    clearFilters() {
        this.searchTerm = '';
        this.selectedSeason = '';
        this.selectedMood = '';
        this.selectedStatus = '';
        window.location.href = window.location.pathname;
    },

    setupDebounce() {
        this.debouncedNavigate = window.utils.debounce((params) => {
            const url = params.toString() ? `${window.location.pathname}?${params.toString()}` : window.location.pathname;
            window.location.href = url;
        }, 500);
    }
}));

// Hero Slideshow Component
Alpine.data('heroSlideshow', () => ({
    currentIndex: 0,
    images: [
        {
            url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
            alt: 'Beautiful mountain landscape with lake reflection'
        },
        {
            url: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2073&q=80',
            alt: 'Tropical beach with crystal clear water'
        },
        {
            url: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
            alt: 'Misty forest with tall trees'
        },
        {
            url: 'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
            alt: 'Northern lights over snowy landscape'
        },
        {
            url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2071&q=80',
            alt: 'Dense green forest from above'
        },
        {
            url: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
            alt: 'Sunset over ocean waves'
        }
    ],

    init() {
        this.startSlideshow();
    },

    startSlideshow() {
        setInterval(() => {
            this.nextSlide();
        }, 8000); // Change image every 8 seconds for smoother experience
    },

    nextSlide() {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
    },

    prevSlide() {
        this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
    },

    goToSlide(index) {
        this.currentIndex = index;
    }
}));

// Profile Photo Upload Component
Alpine.data('profilePhotoUpload', () => ({
    photoPreview: null,

    previewPhoto(event) {
        const file = event.target.files[0];
        if (file) {
            // Validate file size (2MB max)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                event.target.value = '';
                return;
            }

            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                event.target.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.photoPreview = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    },

    removePhoto() {
        this.photoPreview = null;
        document.getElementById('avatar').value = '';
    }
}));

Alpine.data('flashMessage', () => ({
    show: false,
    message: '',
    type: 'success',

    init() {
        // Check for flash messages from Laravel session
        const flashData = document.querySelector('[data-flash]');
        if (flashData) {
            const data = JSON.parse(flashData.dataset.flash);
            this.showMessage(data.message, data.type);
        }
    },

    showMessage(message, type = 'success') {
        this.message = message;
        this.type = type;
        this.show = true;

        // Auto hide after 5 seconds
        setTimeout(() => {
            this.hide();
        }, 5000);
    },

    hide() {
        this.show = false;
        setTimeout(() => {
            this.message = '';
        }, 300);
    }
}));

// Initialize AOS when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // Initialize GSAP animations
    initGSAPAnimations();

    // Initialize smooth image loading
    initImageLoading();
});

function initGSAPAnimations() {
    // Hero section animations
    gsap.from('.hero-title', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    });

    gsap.from('.hero-subtitle', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.3,
        ease: 'power3.out'
    });

    gsap.from('.hero-cta', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.6,
        ease: 'power3.out'
    });

    // Card animations on scroll
    gsap.utils.toArray('.card-animate').forEach((card, index) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            duration: 0.8,
            y: 50,
            opacity: 0,
            delay: index * 0.1,
            ease: 'power3.out'
        });
    });

    // Parallax effect for hero background
    gsap.utils.toArray('.parallax').forEach(element => {
        gsap.to(element, {
            yPercent: -50,
            ease: 'none',
            scrollTrigger: {
                trigger: element,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    });
}

// Utility functions
window.utils = {
    // Format date
    formatDate(date) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },

    // Debounce function for search
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Copy to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('Failed to copy: ', err);
            return false;
        }
    }
};

// Smooth Image Loading Function
function initImageLoading() {
    const images = document.querySelectorAll('.image-reveal');

    images.forEach(img => {
        if (img.complete) {
            img.classList.add('loaded');
        } else {
            img.addEventListener('load', function() {
                this.classList.add('loaded');
            });

            img.addEventListener('error', function() {
                this.style.background = 'linear-gradient(45deg, #f3f4f6, #e5e7eb)';
                this.classList.add('loaded');
            });
        }
    });

    // Intersection Observer for lazy loading
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

Alpine.start();
