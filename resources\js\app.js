import './bootstrap';

import Alpine from 'alpinejs';

// Utility functions
window.utils = {
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    animateValue: function(obj, start, end, duration) {
        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            obj.innerHTML = Math.floor(progress * (end - start) + start);
            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        window.requestAnimationFrame(step);
    }
};
import AOS from 'aos';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Chart from 'chart.js/auto';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Make libraries available globally
window.Alpine = Alpine;
window.AOS = AOS;
window.gsap = gsap;
window.ScrollTrigger = ScrollTrigger;
window.Chart = Chart;

// Alpine.js data components
Alpine.data('darkMode', () => ({
    dark: localStorage.getItem('darkMode') === 'true' ||
          (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches),

    init() {
        this.updateTheme();
    },

    toggle() {
        this.dark = !this.dark;
        this.updateTheme();
        localStorage.setItem('darkMode', this.dark);
    },

    updateTheme() {
        if (this.dark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }
}));

Alpine.data('imagePreview', () => ({
    imageUrl: null,

    previewImage(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.imageUrl = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    },

    removeImage() {
        this.imageUrl = null;
        // Reset file input
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) fileInput.value = '';
    }
}));

Alpine.data('searchFilter', () => ({
    search: '',
    musim: '',
    mood: '',
    status: '',

    get filteredItems() {
        // This will be implemented in the blade templates
        return [];
    },

    clearFilters() {
        this.search = '';
        this.musim = '';
        this.mood = '';
        this.status = '';
    }
}));

// Destination Filter Component
Alpine.data('destinationFilter', () => ({
    searchTerm: '',
    selectedSeason: '',
    selectedMood: '',
    selectedStatus: '',

    init() {
        // Initialize from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        this.searchTerm = urlParams.get('search') || '';
        this.selectedSeason = urlParams.get('musim') || '';
        this.selectedMood = urlParams.get('mood') || '';
        this.selectedStatus = urlParams.get('status') || '';

        // Setup debounced navigation
        this.setupDebounce();
    },

    filterDestinations() {
        const params = new URLSearchParams();

        if (this.searchTerm.trim()) params.append('search', this.searchTerm.trim());
        if (this.selectedSeason) params.append('musim', this.selectedSeason);
        if (this.selectedMood) params.append('mood', this.selectedMood);
        if (this.selectedStatus) params.append('status', this.selectedStatus);

        // Preserve current sort
        const currentSort = new URLSearchParams(window.location.search).get('sort') || 'created_at';
        const currentOrder = new URLSearchParams(window.location.search).get('order') || 'desc';
        params.append('sort', currentSort);
        params.append('order', currentOrder);

        // Use debounced navigation
        this.debouncedNavigate(params);
    },

    clearFilters() {
        this.searchTerm = '';
        this.selectedSeason = '';
        this.selectedMood = '';
        this.selectedStatus = '';
        window.location.href = window.location.pathname;
    },

    setupDebounce() {
        this.debouncedNavigate = window.utils.debounce((params) => {
            const url = params.toString() ? `${window.location.pathname}?${params.toString()}` : window.location.pathname;
            window.location.href = url;
        }, 500);
    }
}));

// Hero Slideshow Component
Alpine.data('heroSlideshow', () => ({
    currentIndex: 0,
    images: [
        {
            url: '/images/hero/hero-mountain-lake.jpg',
            alt: 'Beautiful mountain landscape with lake reflection'
        },
        {
            url: '/images/hero/hero-tropical-beach.jpg',
            alt: 'Tropical beach with crystal clear water'
        },
        {
            url: '/images/hero/hero-misty-forest.jpg',
            alt: 'Misty forest with tall trees'
        },
        {
            url: '/images/hero/hero-northern-lights.jpg',
            alt: 'Northern lights over snowy landscape'
        },
        {
            url: '/images/hero/hero-green-forest.jpg',
            alt: 'Dense green forest from above'
        },
        {
            url: '/images/hero/hero-ocean-sunset.jpg',
            alt: 'Sunset over ocean waves'
        }
    ],

    init() {
        this.startSlideshow();
    },

    startSlideshow() {
        setInterval(() => {
            this.nextSlide();
        }, 8000); // Change image every 8 seconds for smoother experience
    },

    nextSlide() {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
    },

    prevSlide() {
        this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
    },

    goToSlide(index) {
        this.currentIndex = index;
    }
}));

// Profile Photo Upload Component
Alpine.data('profilePhotoUpload', () => ({
    photoPreview: null,

    previewPhoto(event) {
        const file = event.target.files[0];
        if (file) {
            // Validate file size (2MB max)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                event.target.value = '';
                return;
            }

            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                event.target.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.photoPreview = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    },

    removePhoto() {
        this.photoPreview = null;
        document.getElementById('avatar').value = '';
    }
}));

Alpine.data('flashMessage', () => ({
    show: false,
    message: '',
    type: 'success',

    init() {
        // Check for flash messages from Laravel session
        const flashData = document.querySelector('[data-flash]');
        if (flashData) {
            const data = JSON.parse(flashData.dataset.flash);
            this.showMessage(data.message, data.type);
        }
    },

    showMessage(message, type = 'success') {
        this.message = message;
        this.type = type;
        this.show = true;

        // Auto hide after 5 seconds
        setTimeout(() => {
            this.hide();
        }, 5000);
    },

    hide() {
        this.show = false;
        setTimeout(() => {
            this.message = '';
        }, 300);
    }
}));

// Initialize AOS when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 1000,
        easing: 'ease-out-cubic',
        once: true,
        offset: 50,
        delay: 100,
        disable: false,
        startEvent: 'DOMContentLoaded',
        initClassName: 'aos-init',
        animatedClassName: 'aos-animate',
        useClassNames: false,
        disableMutationObserver: false,
        debounceDelay: 50,
        throttleDelay: 99,
    });

    // Initialize GSAP animations
    initGSAPAnimations();

    // Initialize smooth image loading
    initImageLoading();

    // Initialize enhanced animations
    initEnhancedAnimations();
});

function initGSAPAnimations() {
    // Hero section animations
    gsap.from('.hero-title', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    });

    gsap.from('.hero-subtitle', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.3,
        ease: 'power3.out'
    });

    gsap.from('.hero-cta', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.6,
        ease: 'power3.out'
    });

    // Card animations on scroll
    gsap.utils.toArray('.card-animate').forEach((card, index) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            duration: 0.8,
            y: 50,
            opacity: 0,
            delay: index * 0.1,
            ease: 'power3.out'
        });
    });

    // Parallax effect for hero background
    gsap.utils.toArray('.parallax').forEach(element => {
        gsap.to(element, {
            yPercent: -50,
            ease: 'none',
            scrollTrigger: {
                trigger: element,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    });
}

// Utility functions
window.utils = {
    // Format date
    formatDate(date) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },

    // Debounce function for search
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Copy to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('Failed to copy: ', err);
            return false;
        }
    }
};

// Smooth Image Loading Function
function initImageLoading() {
    const images = document.querySelectorAll('.image-reveal');

    images.forEach(img => {
        if (img.complete) {
            img.classList.add('loaded');
        } else {
            img.addEventListener('load', function() {
                this.classList.add('loaded');
            });

            img.addEventListener('error', function() {
                this.style.background = 'linear-gradient(45deg, #f3f4f6, #e5e7eb)';
                this.classList.add('loaded');
            });
        }
    });

    // Intersection Observer for lazy loading
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Enhanced Animations Function
function initEnhancedAnimations() {
    // Smooth scroll behavior with easing
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                gsap.to(window, {
                    duration: 1.5,
                    scrollTo: {
                        y: target,
                        offsetY: 80
                    },
                    ease: "power3.inOut"
                });
            }
        });
    });

    // Advanced hover effects with magnetic attraction
    document.querySelectorAll('.hover-smooth-lift').forEach(element => {
        element.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.4,
                y: -12,
                rotationX: 15,
                rotationY: 5,
                scale: 1.05,
                boxShadow: '0 30px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                ease: 'power2.out'
            });
        });

        element.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.4,
                y: 0,
                rotationX: 0,
                rotationY: 0,
                scale: 1,
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                ease: 'power2.out'
            });
        });
    });

    // Magnetic hover effect
    document.querySelectorAll('.hover-smooth-magnetic').forEach(element => {
        element.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            gsap.to(this, {
                duration: 0.3,
                x: x * 0.1,
                y: y * 0.1,
                rotation: x * 0.05,
                ease: 'power2.out'
            });
        });

        element.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.5,
                x: 0,
                y: 0,
                rotation: 0,
                ease: 'elastic.out(1, 0.3)'
            });
        });
    });

    // Advanced button click animations
    document.querySelectorAll('button, .btn, a[class*="btn"]').forEach(button => {
        button.addEventListener('click', function(e) {
            // Ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            // Button press animation
            gsap.to(this, {
                duration: 0.1,
                scale: 0.95,
                ease: 'power2.out',
                yoyo: true,
                repeat: 1
            });

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Stagger animations for cards with advanced effects
    gsap.utils.toArray('.destination-card, .glass-card, .card').forEach((card, index) => {
        // Initial state
        gsap.set(card, {
            y: 60,
            opacity: 0,
            scale: 0.9,
            rotationY: 15
        });

        // Scroll trigger animation
        gsap.to(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse'
            },
            duration: 0.8,
            y: 0,
            opacity: 1,
            scale: 1,
            rotationY: 0,
            delay: index * 0.1,
            ease: 'back.out(1.7)'
        });

        // Hover animation
        card.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                y: -8,
                scale: 1.02,
                rotationX: 5,
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                y: 0,
                scale: 1,
                rotationX: 0,
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                ease: 'power2.out'
            });
        });
    });

    // Text reveal animations with typewriter effect
    gsap.utils.toArray('.text-reveal, h1, h2, h3').forEach((text, index) => {
        const chars = text.textContent.split('');
        text.innerHTML = chars.map(char => `<span style="display: inline-block;">${char === ' ' ? '&nbsp;' : char}</span>`).join('');

        gsap.from(text.children, {
            scrollTrigger: {
                trigger: text,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            duration: 0.05,
            opacity: 0,
            y: 20,
            rotationX: 90,
            stagger: 0.02,
            ease: 'back.out(1.7)',
            delay: index * 0.1
        });
    });

    // Counter animations with easing
    gsap.utils.toArray('.counter, .animate-count-up').forEach(counter => {
        const target = parseInt(counter.textContent.replace(/\D/g, ''));
        const suffix = counter.textContent.replace(/\d/g, '');

        gsap.set(counter, { textContent: '0' + suffix });

        gsap.to(counter, {
            scrollTrigger: {
                trigger: counter,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            duration: 2,
            textContent: target + suffix,
            ease: 'power2.out',
            snap: { textContent: 1 },
            onUpdate: function() {
                const current = Math.floor(this.targets()[0].textContent.replace(/\D/g, ''));
                counter.textContent = current + suffix;
            }
        });
    });

    // Parallax scrolling effects
    gsap.utils.toArray('.parallax').forEach(element => {
        gsap.to(element, {
            yPercent: -50,
            ease: "none",
            scrollTrigger: {
                trigger: element,
                start: "top bottom",
                end: "bottom top",
                scrub: true
            }
        });
    });

    // Image reveal animations
    gsap.utils.toArray('img').forEach((img, index) => {
        gsap.from(img, {
            scrollTrigger: {
                trigger: img,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse'
            },
            duration: 1,
            scale: 1.2,
            opacity: 0,
            filter: 'blur(10px)',
            delay: index * 0.05,
            ease: 'power3.out'
        });
    });

    // Navigation animations
    gsap.utils.toArray('nav a, .nav-link').forEach((link, index) => {
        link.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.05,
                color: '#3B82F6',
                ease: 'power2.out'
            });
        });

        link.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                color: '',
                ease: 'power2.out'
            });
        });
    });

    // Form input animations
    gsap.utils.toArray('input, textarea, select').forEach(input => {
        input.addEventListener('focus', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.02,
                boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',
                ease: 'power2.out'
            });
        });

        input.addEventListener('blur', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                boxShadow: '',
                ease: 'power2.out'
            });
        });
    });

    // Loading animations
    initLoadingAnimations();

    // Cursor trail effect
    initCursorTrail();
}

// Add ripple effect CSS
const rippleCSS = `
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Loading Animations
function initLoadingAnimations() {
    // Page loading animation
    window.addEventListener('load', function() {
        const loader = document.querySelector('.page-loader');
        if (loader) {
            gsap.to(loader, {
                duration: 0.8,
                opacity: 0,
                scale: 1.1,
                ease: 'power2.inOut',
                onComplete: () => loader.remove()
            });
        }

        // Animate page elements on load
        gsap.from('body > *', {
            duration: 0.8,
            y: 30,
            opacity: 0,
            stagger: 0.1,
            ease: 'power3.out',
            delay: 0.2
        });
    });

    // AJAX loading states
    document.addEventListener('htmx:beforeRequest', function() {
        const loader = document.createElement('div');
        loader.className = 'loading-overlay';
        loader.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
        `;
        document.body.appendChild(loader);

        gsap.from(loader, {
            duration: 0.3,
            opacity: 0,
            scale: 0.8,
            ease: 'power2.out'
        });
    });

    document.addEventListener('htmx:afterRequest', function() {
        const loader = document.querySelector('.loading-overlay');
        if (loader) {
            gsap.to(loader, {
                duration: 0.3,
                opacity: 0,
                scale: 1.2,
                ease: 'power2.in',
                onComplete: () => loader.remove()
            });
        }
    });
}

// Cursor Trail Effect
function initCursorTrail() {
    const trail = [];
    const trailLength = 20;

    // Create trail elements
    for (let i = 0; i < trailLength; i++) {
        const dot = document.createElement('div');
        dot.className = 'cursor-trail';
        dot.style.cssText = `
            position: fixed;
            width: 4px;
            height: 4px;
            background: linear-gradient(45deg, #3B82F6, #8B5CF6);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            opacity: ${1 - i / trailLength};
            transform: scale(${1 - i / trailLength * 0.5});
        `;
        document.body.appendChild(dot);
        trail.push(dot);
    }

    let mouseX = 0, mouseY = 0;
    let currentX = 0, currentY = 0;

    document.addEventListener('mousemove', function(e) {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    function animateTrail() {
        currentX += (mouseX - currentX) * 0.1;
        currentY += (mouseY - currentY) * 0.1;

        trail.forEach((dot, index) => {
            const nextDot = trail[index + 1] || { offsetLeft: currentX, offsetTop: currentY };

            gsap.to(dot, {
                duration: 0.3,
                x: nextDot.offsetLeft || currentX,
                y: nextDot.offsetTop || currentY,
                ease: 'power2.out'
            });
        });

        requestAnimationFrame(animateTrail);
    }

    animateTrail();
}

// Particle System
function initParticleSystem() {
    const canvas = document.createElement('canvas');
    canvas.id = 'particle-canvas';
    canvas.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
        opacity: 0.6;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    const particles = [];
    const particleCount = 50;

    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    function createParticle() {
        return {
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 2 + 1,
            opacity: Math.random() * 0.5 + 0.2
        };
    }

    function initParticles() {
        for (let i = 0; i < particleCount; i++) {
            particles.push(createParticle());
        }
    }

    function updateParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;

            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(59, 130, 246, ${particle.opacity})`;
            ctx.fill();
        });

        requestAnimationFrame(updateParticles);
    }

    resizeCanvas();
    initParticles();
    updateParticles();

    window.addEventListener('resize', resizeCanvas);
}

// Advanced Scroll Animations
function initAdvancedScrollAnimations() {
    // Smooth reveal on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-smooth-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all elements with animation classes
    document.querySelectorAll('[class*="animate-"], .card, .destination-card, .glass-card').forEach(el => {
        observer.observe(el);
    });

    // Parallax backgrounds
    gsap.utils.toArray('.parallax-bg').forEach(bg => {
        gsap.to(bg, {
            yPercent: -30,
            ease: "none",
            scrollTrigger: {
                trigger: bg,
                start: "top bottom",
                end: "bottom top",
                scrub: 1
            }
        });
    });
}

Alpine.start();

// Professional Animation System
class ElegantAnimations {
    constructor() {
        this.isTouch = 'ontouchstart' in window;
        this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.init();
    }

    init() {
        if (this.prefersReducedMotion) return;

        this.initScrollAnimations();
        this.initHoverEffects();
        this.initParallax();
        this.initTextAnimations();
        this.initLoadingAnimations();
        this.initResponsiveAnimations();

        if (!this.isTouch) {
            this.initCursorEffects();
            this.initMagneticElements();
        }
    }

    initScrollAnimations() {
        // Enhanced scroll-triggered animations
        const observerOptions = {
            threshold: [0, 0.1, 0.2, 0.3, 0.5, 0.7, 1],
            rootMargin: '-10% 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const delay = element.dataset.delay || 0;

                    setTimeout(() => {
                        element.classList.add('animate-elegant-entrance');
                        this.animateChildren(element);
                    }, delay);

                    observer.unobserve(element);
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('[data-animate], .card-elegant, .destination-card').forEach((el, index) => {
            el.dataset.delay = index * 100;
            observer.observe(el);
        });
    }

    animateChildren(parent) {
        const children = parent.querySelectorAll('.text-reveal, h1, h2, h3, p, .btn-elegant-primary, .btn-elegant-secondary');

        children.forEach((child, index) => {
            setTimeout(() => {
                child.classList.add('animate-elegant-slide-up');
            }, index * 50);
        });
    }

    initHoverEffects() {
        // Enhanced hover effects for cards
        document.querySelectorAll('.card-elegant').forEach(card => {
            card.addEventListener('mouseenter', (e) => {
                if (this.isTouch) return;

                gsap.to(card, {
                    duration: 0.6,
                    y: -20,
                    rotationX: 8,
                    rotationY: 4,
                    scale: 1.03,
                    boxShadow: '0 40px 80px rgba(0, 0, 0, 0.25)',
                    ease: 'power3.out'
                });

                // Animate card content
                const content = card.querySelectorAll('h3, p, .btn-elegant-primary, .btn-elegant-secondary');
                gsap.to(content, {
                    duration: 0.4,
                    y: -4,
                    stagger: 0.05,
                    ease: 'power2.out'
                });
            });

            card.addEventListener('mouseleave', (e) => {
                if (this.isTouch) return;

                gsap.to(card, {
                    duration: 0.6,
                    y: 0,
                    rotationX: 0,
                    rotationY: 0,
                    scale: 1,
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                    ease: 'power3.out'
                });

                const content = card.querySelectorAll('h3, p, .btn-elegant-primary, .btn-elegant-secondary');
                gsap.to(content, {
                    duration: 0.4,
                    y: 0,
                    stagger: 0.05,
                    ease: 'power2.out'
                });
            });
        });

        // Button hover effects
        document.querySelectorAll('.btn-elegant-primary, .btn-elegant-secondary').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                if (this.isTouch) return;

                gsap.to(btn, {
                    duration: 0.3,
                    scale: 1.05,
                    y: -4,
                    ease: 'power2.out'
                });
            });

            btn.addEventListener('mouseleave', () => {
                if (this.isTouch) return;

                gsap.to(btn, {
                    duration: 0.3,
                    scale: 1,
                    y: 0,
                    ease: 'power2.out'
                });
            });
        });
    }

    initParallax() {
        // Smooth parallax scrolling
        gsap.utils.toArray('.parallax-bg').forEach(bg => {
            gsap.to(bg, {
                yPercent: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: bg,
                    start: "top bottom",
                    end: "bottom top",
                    scrub: 1.5
                }
            });
        });

        // Parallax for hero elements
        gsap.utils.toArray('.hero-parallax').forEach((element, index) => {
            gsap.to(element, {
                yPercent: -30 * (index + 1),
                ease: "none",
                scrollTrigger: {
                    trigger: element,
                    start: "top bottom",
                    end: "bottom top",
                    scrub: 2
                }
            });
        });
    }

    initTextAnimations() {
        // Advanced text reveal animations
        gsap.utils.toArray('.text-elegant-display, .text-elegant-title').forEach(text => {
            const chars = text.textContent.split('');
            text.innerHTML = chars.map(char =>
                `<span class="char" style="display: inline-block;">${char === ' ' ? '&nbsp;' : char}</span>`
            ).join('');

            gsap.from(text.querySelectorAll('.char'), {
                duration: 0.8,
                opacity: 0,
                y: 100,
                rotationX: -90,
                stagger: 0.02,
                ease: 'back.out(1.7)',
                scrollTrigger: {
                    trigger: text,
                    start: 'top 80%',
                    toggleActions: 'play none none reverse'
                }
            });
        });

        // Typewriter effect for subtitles
        gsap.utils.toArray('.text-elegant-subtitle').forEach(text => {
            gsap.from(text, {
                duration: 1.5,
                width: 0,
                ease: 'power2.inOut',
                scrollTrigger: {
                    trigger: text,
                    start: 'top 80%',
                    toggleActions: 'play none none reverse'
                }
            });
        });
    }

    initLoadingAnimations() {
        // Page load animations
        const tl = gsap.timeline();

        tl.from('nav', {
            duration: 1,
            y: -100,
            opacity: 0,
            ease: 'power3.out'
        })
        .from('.hero-content', {
            duration: 1.2,
            y: 60,
            opacity: 0,
            ease: 'power3.out'
        }, '-=0.5')
        .from('.hero-stats', {
            duration: 1,
            y: 40,
            opacity: 0,
            stagger: 0.1,
            ease: 'power3.out'
        }, '-=0.3');
    }

    initResponsiveAnimations() {
        // Responsive animation adjustments
        const mm = gsap.matchMedia();

        mm.add("(max-width: 768px)", () => {
            // Mobile-specific animations
            gsap.set('.card-elegant', {
                scale: 1,
                y: 0,
                rotationX: 0,
                rotationY: 0
            });

            // Simplified mobile animations
            document.querySelectorAll('.card-elegant').forEach(card => {
                card.addEventListener('touchstart', () => {
                    gsap.to(card, {
                        duration: 0.2,
                        scale: 0.98,
                        ease: 'power2.out'
                    });
                });

                card.addEventListener('touchend', () => {
                    gsap.to(card, {
                        duration: 0.3,
                        scale: 1,
                        ease: 'power2.out'
                    });
                });
            });
        });

        mm.add("(min-width: 769px)", () => {
            // Desktop-specific animations
            this.initAdvancedHoverEffects();
        });
    }

    initAdvancedHoverEffects() {
        // Advanced 3D hover effects for desktop
        document.querySelectorAll('.hover-elegant-tilt').forEach(element => {
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                const rotateX = (y - centerY) / centerY * -10;
                const rotateY = (x - centerX) / centerX * 10;

                gsap.to(element, {
                    duration: 0.3,
                    rotationX: rotateX,
                    rotationY: rotateY,
                    transformPerspective: 1000,
                    ease: 'power2.out'
                });
            });

            element.addEventListener('mouseleave', () => {
                gsap.to(element, {
                    duration: 0.5,
                    rotationX: 0,
                    rotationY: 0,
                    ease: 'power2.out'
                });
            });
        });
    }

    initCursorEffects() {
        // Custom cursor for desktop
        const cursor = document.createElement('div');
        cursor.className = 'custom-cursor';
        cursor.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #3B82F6, #8B5CF6);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            mix-blend-mode: difference;
            transition: transform 0.2s ease;
        `;
        document.body.appendChild(cursor);

        let mouseX = 0, mouseY = 0;
        let cursorX = 0, cursorY = 0;

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        function animateCursor() {
            cursorX += (mouseX - cursorX) * 0.1;
            cursorY += (mouseY - cursorY) * 0.1;

            cursor.style.left = cursorX - 10 + 'px';
            cursor.style.top = cursorY - 10 + 'px';

            requestAnimationFrame(animateCursor);
        }
        animateCursor();

        // Cursor interactions
        document.querySelectorAll('a, button, .card-elegant').forEach(el => {
            el.addEventListener('mouseenter', () => {
                cursor.style.transform = 'scale(2)';
            });
            el.addEventListener('mouseleave', () => {
                cursor.style.transform = 'scale(1)';
            });
        });
    }

    initMagneticElements() {
        // Magnetic effect for interactive elements
        document.querySelectorAll('.hover-elegant-magnetic').forEach(element => {
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;

                gsap.to(element, {
                    duration: 0.3,
                    x: x * 0.1,
                    y: y * 0.1,
                    ease: 'power2.out'
                });
            });

            element.addEventListener('mouseleave', () => {
                gsap.to(element, {
                    duration: 0.5,
                    x: 0,
                    y: 0,
                    ease: 'elastic.out(1, 0.3)'
                });
            });
        });
    }
}

// Initialize all advanced animations
document.addEventListener('DOMContentLoaded', function() {
    new ElegantAnimations();
    initParticleSystem();
    initAdvancedScrollAnimations();
});
