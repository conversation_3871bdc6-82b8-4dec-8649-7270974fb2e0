<x-guest-layout>
    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email')" />
            <x-text-input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')" required autofocus autocomplete="username" />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <x-input-label for="password" :value="__('Password')" />
            <x-text-input id="password" class="block mt-1 w-full"
                          type="password"
                          name="password"
                          required autocomplete="current-password" />
            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Remember Me -->
        <div class="block mt-4">
            <label for="remember_me" class="inline-flex items-center">
                <input id="remember_me" type="checkbox"
                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                       name="remember">
                <span class="ms-2 text-sm text-gray-600">{{ __('Remember me') }}</span>
            </label>
        </div>

        <div class="flex items-center justify-end mt-4">
            @if (Route::has('password.request'))
                <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                   href="{{ route('password.request') }}">
                    {{ __('Forgot your password?') }}
                </a>
            @endif

            <x-primary-button class="ms-3">
                {{ __('Log in') }}
            </x-primary-button>
        </div>
    </form>

    <!-- Divider -->
    <div class="flex items-center my-6">
        <div class="flex-grow border-t border-gray-300"></div>
        <span class="mx-4 text-sm text-gray-500">OR</span>
        <div class="flex-grow border-t border-gray-300"></div>
    </div>

    <!-- Google Login -->
    <div class="flex justify-center">
        <a href="{{ route('google.redirect') }}"
           class="w-full sm:w-auto inline-flex items-center px-5 py-2.5 bg-white border border-gray-300 text-gray-700 font-semibold text-sm rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition">
            <svg class="w-5 h-5 mr-2" viewBox="0 0 533.5 544.3" xmlns="http://www.w3.org/2000/svg">
                <path fill="#4285F4" d="M533.5 278.4c0-17.4-1.5-34.5-4.5-51H272v96.7h146.9c-6.4 34.7-25 64-53.2 83.5v69h85.8c50.1-46.1 81-114.2 81-198.2z"/>
                <path fill="#34A853" d="M272 544.3c72.2 0 132.8-23.9 177-64.8l-85.8-69c-23.8 16-54.1 25.4-91.2 25.4-69.9 0-129.1-47.2-150.3-110.7H35.7v69.5c44.2 87.6 135.2 149.6 236.3 149.6z"/>
                <path fill="#FBBC05" d="M121.7 325.2c-10.2-30.6-10.2-63.6 0-94.2V161.5H35.7c-38.5 77.1-38.5 168.2 0 245.3l86-69.6z"/>
                <path fill="#EA4335" d="M272 107.1c37.6 0 71.6 13 98.4 38.5l73.7-73.7C404.7 24.2 344.1 0 272 0 170.9 0 79.9 61.9 35.7 149.5l86 69.5C142.9 154.3 202.1 107.1 272 107.1z"/>
            </svg>
            Login with Google
        </a>
    </div>
</x-guest-layout>
