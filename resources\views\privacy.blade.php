<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="darkMode()" :class="{ 'dark': dark }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Privacy Policy - Dream Destinations</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                                </svg>
                            </div>
                            <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Dream Destinations
                            </span>
                        </a>
                    </div>
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 font-medium transition-colors">
                            Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Privacy Policy</h1>
                
                <div class="prose prose-lg dark:prose-invert max-w-none">
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        <strong>Last updated:</strong> {{ date('F d, Y') }}
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Information We Collect</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        When you use Dream Destinations, we may collect the following information:
                    </p>
                    <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 mb-6">
                        <li>Name and email address (when you register or sign in with Google)</li>
                        <li>Profile picture (from your Google account, if provided)</li>
                        <li>Destination information you add to the platform</li>
                        <li>Usage data and preferences</li>
                    </ul>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">How We Use Your Information</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        We use your information to:
                    </p>
                    <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 mb-6">
                        <li>Provide and maintain our service</li>
                        <li>Allow you to manage your dream destinations</li>
                        <li>Improve user experience</li>
                        <li>Communicate with you about service updates</li>
                    </ul>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Data Security</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Third-Party Services</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        We use Google OAuth for authentication. Please review Google's Privacy Policy for information about how Google handles your data.
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Contact Us</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        If you have any questions about this Privacy Policy, please contact us at: 
                        <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
