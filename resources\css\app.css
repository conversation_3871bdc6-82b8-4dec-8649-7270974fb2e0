@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import 'aos/dist/aos.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===================================
   DREAM DESTINATIONS - DESIGN SYSTEM
   Professional Global Website Framework
   =================================== */

/* CSS Variables for Design System */
:root {
    /* Enhanced Color Palette */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;

    --secondary-50: #fdf4ff;
    --secondary-100: #fae8ff;
    --secondary-200: #f5d0fe;
    --secondary-300: #f0abfc;
    --secondary-400: #e879f9;
    --secondary-500: #d946ef;
    --secondary-600: #c026d3;
    --secondary-700: #a21caf;
    --secondary-800: #86198f;
    --secondary-900: #701a75;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Accent Colors */
    --success-500: #10b981;
    --success-600: #059669;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --error-500: #ef4444;
    --error-600: #dc2626;

    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 0.75rem;    /* 12px */
    --space-lg: 1rem;       /* 16px */
    --space-xl: 1.25rem;    /* 20px */
    --space-2xl: 1.5rem;    /* 24px */
    --space-3xl: 2rem;      /* 32px */
    --space-4xl: 2.5rem;    /* 40px */
    --space-5xl: 3rem;      /* 48px */
    --space-6xl: 4rem;      /* 64px */
    --space-7xl: 5rem;      /* 80px */
    --space-8xl: 6rem;      /* 96px */

    /* Typography Scale */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    --text-6xl: 3.75rem;    /* 60px */
    --text-7xl: 4.5rem;     /* 72px */

    /* Border Radius */
    --radius-sm: 0.375rem;  /* 6px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-3xl: 2rem;     /* 32px */

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* Dark Mode Variables */
.dark {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
}

/* ===================================
   BASE STYLES & TYPOGRAPHY
   =================================== */

/* Base Typography */
html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

body {
    font-size: var(--text-base);
    color: var(--gray-900);
    background-color: var(--gray-50);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography Scale - Responsive dan Smooth */
.text-display-2xl {
    font-size: 2.5rem;
    line-height: 1.1;
    font-weight: 800;
    letter-spacing: -0.025em;
    text-align: center;
    margin-bottom: 1.5rem;
}

.text-display-xl {
    font-size: 2.25rem;
    line-height: 1.1;
    font-weight: 800;
    letter-spacing: -0.025em;
    text-align: center;
    margin-bottom: 1.25rem;
}

.text-display-lg {
    font-size: 2rem;
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.02em;
    margin-bottom: 1rem;
}

.text-display-md {
    font-size: 1.875rem;
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.02em;
    margin-bottom: 1rem;
}

.text-display-sm {
    font-size: 1.5rem;
    line-height: 1.25;
    font-weight: 600;
    letter-spacing: -0.015em;
    margin-bottom: 0.75rem;
}

/* Responsive Typography Scaling */
@media (min-width: 640px) {
    .text-display-2xl { font-size: 3rem; }
    .text-display-xl { font-size: 2.75rem; }
    .text-display-lg { font-size: 2.5rem; }
    .text-display-md { font-size: 2.25rem; }
    .text-display-sm { font-size: 1.875rem; }
}

@media (min-width: 768px) {
    .text-display-2xl { font-size: 3.75rem; }
    .text-display-xl { font-size: 3.5rem; }
    .text-display-lg { font-size: 3rem; }
    .text-display-md { font-size: 2.5rem; }
    .text-display-sm { font-size: 2rem; }
}

@media (min-width: 1024px) {
    .text-display-2xl { font-size: 4.5rem; }
    .text-display-xl { font-size: 4rem; }
    .text-display-lg { font-size: 3.5rem; }
    .text-display-md { font-size: 3rem; }
    .text-display-sm { font-size: 2.25rem; }
}

.text-heading-xl {
    font-size: var(--text-2xl);
    line-height: 1.3;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.text-heading-lg {
    font-size: var(--text-xl);
    line-height: 1.4;
    font-weight: 600;
}

.text-heading-md {
    font-size: var(--text-lg);
    line-height: 1.4;
    font-weight: 600;
}

.text-heading-sm {
    font-size: var(--text-base);
    line-height: 1.5;
    font-weight: 600;
}

.text-body-lg {
    font-size: var(--text-lg);
    line-height: 1.6;
    font-weight: 400;
}

.text-body-md {
    font-size: var(--text-base);
    line-height: 1.6;
    font-weight: 400;
}

.text-body-sm {
    font-size: var(--text-sm);
    line-height: 1.5;
    font-weight: 400;
}

.text-caption {
    font-size: var(--text-xs);
    line-height: 1.4;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Responsive Typography */
@media (max-width: 640px) {
    .text-display-2xl { font-size: var(--text-5xl); }
    .text-display-xl { font-size: var(--text-4xl); }
    .text-display-lg { font-size: var(--text-3xl); }
    .text-display-md { font-size: var(--text-2xl); }
    .text-display-sm { font-size: var(--text-xl); }
}

@media (min-width: 1024px) {
    .text-display-2xl { font-size: 5rem; }
    .text-display-xl { font-size: 4.5rem; }
}

/* Text Colors */
.text-primary { color: var(--primary-600); }
.text-secondary { color: var(--secondary-600); }
.text-gray-50 { color: var(--gray-50); }
.text-gray-100 { color: var(--gray-100); }
.text-gray-200 { color: var(--gray-200); }
.text-gray-300 { color: var(--gray-300); }
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }
.text-white { color: #ffffff; }

/* ===================================
   LAYOUT SYSTEM & SPACING
   =================================== */

/* Container System - Rapih dan Tidak Offside */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.container-2xl {
    max-width: 1536px;
}

/* Responsive Container Padding */
@media (min-width: 640px) {
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 768px) {
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding-left: 2.5rem;
        padding-right: 2.5rem;
    }
}

@media (min-width: 1280px) {
    .container {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

/* Section Spacing */
.section {
    padding-top: var(--space-6xl);
    padding-bottom: var(--space-6xl);
}

.section-sm {
    padding-top: var(--space-5xl);
    padding-bottom: var(--space-5xl);
}

.section-lg {
    padding-top: var(--space-7xl);
    padding-bottom: var(--space-7xl);
}

.section-xl {
    padding-top: var(--space-8xl);
    padding-bottom: var(--space-8xl);
}

@media (min-width: 768px) {
    .section {
        padding-top: var(--space-7xl);
        padding-bottom: var(--space-7xl);
    }

    .section-sm {
        padding-top: var(--space-6xl);
        padding-bottom: var(--space-6xl);
    }

    .section-lg {
        padding-top: var(--space-8xl);
        padding-bottom: var(--space-8xl);
    }
}

@media (min-width: 1024px) {
    .section {
        padding-top: 6rem;
        padding-bottom: 6rem;
    }

    .section-lg {
        padding-top: 8rem;
        padding-bottom: 8rem;
    }

    .section-xl {
        padding-top: 10rem;
        padding-bottom: 10rem;
    }
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-2xl);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

@media (max-width: 640px) {
    .grid {
        gap: var(--space-lg);
    }

    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4,
    .grid-cols-5,
    .grid-cols-6 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .grid-cols-3,
    .grid-cols-4,
    .grid-cols-5,
    .grid-cols-6 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .grid-cols-4,
    .grid-cols-5,
    .grid-cols-6 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1025px) {
    .grid {
        gap: var(--space-3xl);
    }
}

/* Flex System */
.flex {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.flex-col {
    flex-direction: column;
    align-items: stretch;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.self-start { align-self: flex-start; }
.self-center { align-self: center; }
.self-end { align-self: flex-end; }
.self-stretch { align-self: stretch; }

/* Responsive Flex */
@media (max-width: 640px) {
    .flex-responsive {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }
}

@media (min-width: 641px) {
    .flex-responsive {
        flex-direction: row;
        align-items: center;
        gap: var(--space-lg);
    }
}

/* ===================================
   COMPONENT SYSTEM
   =================================== */

/* Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-2xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--text-sm);
    line-height: 1;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: 44px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-700), var(--secondary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
    box-shadow: none;
}

.btn-outline:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-ghost {
    background: transparent;
    color: var(--gray-700);
    box-shadow: none;
}

.btn-ghost:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.dark .btn-ghost {
    color: var(--gray-300);
}

.dark .btn-ghost:hover {
    background: var(--gray-800);
    color: var(--gray-100);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-xs);
    min-height: 36px;
}

.btn-lg {
    padding: var(--space-lg) var(--space-3xl);
    font-size: var(--text-base);
    min-height: 52px;
}

.btn-xl {
    padding: var(--space-xl) var(--space-4xl);
    font-size: var(--text-lg);
    min-height: 60px;
}

/* Responsive Buttons */
@media (max-width: 640px) {
    .btn-responsive {
        width: 100%;
        justify-content: center;
    }
}

@media (min-width: 641px) {
    .btn-responsive {
        width: auto;
    }
}

/* Card System */
.card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.dark .card {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-compact {
    padding: var(--space-lg);
}

.card-spacious {
    padding: var(--space-4xl);
}

.card-flat {
    box-shadow: var(--shadow-sm);
}

.card-elevated {
    box-shadow: var(--shadow-2xl);
}

/* Glass Card */
.card-glass {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
}

.dark .card-glass {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===================================
   ANIMATION SYSTEM
   =================================== */

/* Base Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-30px); }
    70% { transform: translateY(-15px); }
    90% { transform: translateY(-4px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-down {
    animation: fadeInDown 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 8s ease-in-out infinite;
}

/* Animation Delays */
.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-400 { animation-delay: 400ms; }
.animate-delay-500 { animation-delay: 500ms; }
.animate-delay-600 { animation-delay: 600ms; }
.animate-delay-700 { animation-delay: 700ms; }
.animate-delay-800 { animation-delay: 800ms; }

/* Hover Animations */
.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-8px);
}

.hover-scale {
    transition: transform var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading States */
.loading-dots {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
    justify-content: center;
}

.loading-dots div {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-500);
    animation: pulse 1.4s ease-in-out infinite both;
}

.loading-dots div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots div:nth-child(2) { animation-delay: -0.16s; }
.loading-dots div:nth-child(3) { animation-delay: 0s; }
.loading-dots div:nth-child(4) { animation-delay: 0.16s; }

/* ===================================
   NAVIGATION SYSTEM
   =================================== */

/* Navigation Links */
.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
}

.nav-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
    transform: translateY(-1px);
}

.nav-link-active {
    color: var(--primary-600);
    background: var(--primary-100);
    font-weight: 600;
}

.nav-link-active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: var(--primary-600);
    border-radius: 2px;
}

.dark .nav-link {
    color: var(--gray-300);
}

.dark .nav-link:hover {
    color: var(--primary-400);
    background: var(--primary-900);
}

.dark .nav-link-active {
    color: var(--primary-400);
    background: var(--primary-900);
}

.dark .nav-link-active::after {
    background: var(--primary-400);
}

/* Mobile Navigation */
.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-lg);
    font-weight: 500;
    color: var(--gray-700);
    text-decoration: none;
    border-bottom: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.mobile-nav-link:hover {
    background: var(--gray-50);
    color: var(--primary-600);
}

.mobile-nav-link-active {
    background: var(--primary-50);
    color: var(--primary-600);
    font-weight: 600;
}

.dark .mobile-nav-link {
    color: var(--gray-300);
    border-color: var(--gray-700);
}

.dark .mobile-nav-link:hover {
    background: var(--gray-800);
    color: var(--primary-400);
}

.dark .mobile-nav-link-active {
    background: var(--primary-900);
    color: var(--primary-400);
}

/* ===================================
   UTILITY CLASSES
   =================================== */

/* Spacing */
.space-y-1 > * + * { margin-top: var(--space-xs); }
.space-y-2 > * + * { margin-top: var(--space-sm); }
.space-y-3 > * + * { margin-top: var(--space-md); }
.space-y-4 > * + * { margin-top: var(--space-lg); }
.space-y-6 > * + * { margin-top: var(--space-2xl); }
.space-y-8 > * + * { margin-top: var(--space-3xl); }

.space-x-1 > * + * { margin-left: var(--space-xs); }
.space-x-2 > * + * { margin-left: var(--space-sm); }
.space-x-3 > * + * { margin-left: var(--space-md); }
.space-x-4 > * + * { margin-left: var(--space-lg); }
.space-x-6 > * + * { margin-left: var(--space-2xl); }
.space-x-8 > * + * { margin-left: var(--space-3xl); }

/* Margins */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-xs); }
.m-2 { margin: var(--space-sm); }
.m-3 { margin: var(--space-md); }
.m-4 { margin: var(--space-lg); }
.m-6 { margin: var(--space-2xl); }
.m-8 { margin: var(--space-3xl); }

.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-3 { margin-top: var(--space-md); }
.mt-4 { margin-top: var(--space-lg); }
.mt-6 { margin-top: var(--space-2xl); }
.mt-8 { margin-top: var(--space-3xl); }
.mt-12 { margin-top: var(--space-5xl); }
.mt-16 { margin-top: var(--space-6xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }
.mb-6 { margin-bottom: var(--space-2xl); }
.mb-8 { margin-bottom: var(--space-3xl); }
.mb-12 { margin-bottom: var(--space-5xl); }
.mb-16 { margin-bottom: var(--space-6xl); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-xs); }
.ml-2 { margin-left: var(--space-sm); }
.ml-3 { margin-left: var(--space-md); }
.ml-4 { margin-left: var(--space-lg); }
.ml-6 { margin-left: var(--space-2xl); }
.ml-8 { margin-left: var(--space-3xl); }
.ml-10 { margin-left: 2.5rem; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-xs); }
.mr-2 { margin-right: var(--space-sm); }
.mr-3 { margin-right: var(--space-md); }
.mr-4 { margin-right: var(--space-lg); }
.mr-6 { margin-right: var(--space-2xl); }
.mr-8 { margin-right: var(--space-3xl); }

/* Glass Effects */
.glass-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-button {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Professional Buttons */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: inherit;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Cards */
.destination-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .destination-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.destination-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Feature Cards */
.feature-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 2rem;
    padding: 2rem;
    transition: all 0.4s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .feature-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
}

/* Stats Cards */
.stats-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .stats-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Typography */
.hero-title {
    font-size: 3.5rem;
    line-height: 1.1;
    font-weight: 800;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #1e293b, #3b82f6, #8b5cf6);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.section-title {
    font-size: 2.5rem;
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.card-title {
    font-size: 1.5rem;
    line-height: 1.3;
    font-weight: 600;
}

/* Responsive Typography */
@media (max-width: 640px) {
    .hero-title { font-size: 2.5rem; }
    .section-title { font-size: 2rem; }
    .card-title { font-size: 1.25rem; }
}

@media (min-width: 1024px) {
    .hero-title { font-size: 4rem; }
    .section-title { font-size: 3rem; }
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container { padding: 0 1.5rem; }
}

@media (min-width: 1024px) {
    .container { padding: 0 2rem; }
}

.section {
    padding: 5rem 0;
}

@media (min-width: 768px) {
    .section { padding: 6rem 0; }
}

@media (min-width: 1024px) {
    .section { padding: 8rem 0; }
}

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Inter', sans-serif;
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100 dark:bg-gray-800;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-400 dark:bg-gray-600 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-500 dark:bg-gray-500;
    }
}

@layer components {
    /* Glass morphism components */
    .glass {
        -webkit-backdrop-filter: blur(16px);
        backdrop-filter: blur(16px);
    }

    .glass-card {
        @apply backdrop-blur-md bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 rounded-xl;
    }

    .glass-button {
        @apply glass backdrop-blur-md bg-white/20 dark:bg-black/20 border border-white/30 dark:border-white/20 hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-300;
    }

    /* Neumorphism components */
    .neuro-card {
        @apply bg-gray-100 dark:bg-gray-800 rounded-2xl shadow-neumorphism dark:shadow-none;
    }

    .neuro-button {
        @apply bg-gray-100 dark:bg-gray-800 rounded-xl shadow-neumorphism dark:shadow-none hover:shadow-neumorphism-inset dark:hover:shadow-none transition-all duration-300;
    }

    /* Gradient backgrounds */
    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-bg-2 {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .gradient-bg-3 {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .gradient-bg-4 {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    /* Animated gradients */
    .animated-gradient {
        background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
    }

    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Button styles */
    .btn-primary {
        @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    .btn-secondary {
        @apply bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    .btn-accent {
        @apply bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    /* Card styles */
    .card {
        @apply bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-200 dark:border-gray-700;
    }

    /* Line clamp utility */
    .line-clamp-2 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    .card-glass {
        @apply glass-card hover:bg-white/20 dark:hover:bg-black/20 transform hover:scale-105 transition-all duration-300;
    }

    /* Input styles */
    .input-modern {
        @apply w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-300;
    }

    /* Loading animations */
    .skeleton {
        @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
    }

    .loading-dots {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 80px;
    }

    .loading-dots div {
        position: absolute;
        top: 33px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: #3b82f6;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }

    .loading-dots div:nth-child(1) {
        left: 8px;
        animation: loading-dots1 0.6s infinite;
    }

    .loading-dots div:nth-child(2) {
        left: 8px;
        animation: loading-dots2 0.6s infinite;
    }

    .loading-dots div:nth-child(3) {
        left: 32px;
        animation: loading-dots2 0.6s infinite;
    }

    .loading-dots div:nth-child(4) {
        left: 56px;
        animation: loading-dots3 0.6s infinite;
    }

    @keyframes loading-dots1 {
        0% { transform: scale(0); }
        100% { transform: scale(1); }
    }

    @keyframes loading-dots3 {
        0% { transform: scale(1); }
        100% { transform: scale(0); }
    }

    @keyframes loading-dots2 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(24px, 0); }
    }

    /* Advanced Animations */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    @keyframes pulse-glow {
        0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
        50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
    }

    @keyframes slide-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slide-in-left {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slide-in-right {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes scale-in {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }

    /* Animation Classes */
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }

    .animate-pulse-glow {
        animation: pulse-glow 2s ease-in-out infinite;
    }

    .animate-slide-in-up {
        animation: slide-in-up 0.6s ease-out;
    }

    .animate-slide-in-left {
        animation: slide-in-left 0.6s ease-out;
    }

    .animate-slide-in-right {
        animation: slide-in-right 0.6s ease-out;
    }

    .animate-fade-in {
        animation: fade-in 0.8s ease-out;
    }

    .animate-scale-in {
        animation: scale-in 0.5s ease-out;
    }

    .animate-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }

    /* Hover Effects */
    .hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-lift:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .hover-glow:hover {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
    }

    /* Glass Card Enhanced */
    .glass-card-enhanced {
        background: rgba(255, 255, 255, 0.1);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    /* Smooth Transitions */
    .transition-smooth {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .transition-bounce {
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* Image Optimization */
    .destination-image {
        object-fit: cover;
        object-position: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    .destination-image:hover {
        transform: scale(1.05);
        filter: brightness(1.1) contrast(1.05);
    }

    /* Image Loading States */
    .image-loading {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Smooth Image Reveal */
    .image-reveal {
        opacity: 0;
        transition: opacity 0.6s ease-in-out;
    }

    .image-reveal.loaded {
        opacity: 1;
    }

    /* Aspect Ratio Containers */
    .aspect-ratio-4-3 {
        aspect-ratio: 4/3;
    }

    .aspect-ratio-16-9 {
        aspect-ratio: 16/9;
    }

    .aspect-ratio-3-2 {
        aspect-ratio: 3/2;
    }

    /* New Enhanced Animations */
    .animate-float-slow {
        animation: floatSlow 6s ease-in-out infinite;
    }

    .animate-count-up {
        animation: countUp 2s ease-out forwards;
    }

    .animate-wiggle {
        animation: wiggle 1s ease-in-out infinite;
    }

    .animate-heartbeat {
        animation: heartbeat 1.5s ease-in-out infinite;
    }

    .animate-slide-down {
        animation: slideDown 0.5s ease-out forwards;
    }

    .animate-slide-up {
        animation: slideUp 0.5s ease-out forwards;
    }

    .animate-zoom-in {
        animation: zoomIn 0.5s ease-out forwards;
    }

    .animate-zoom-out {
        animation: zoomOut 0.5s ease-out forwards;
    }

    .animate-flip {
        animation: flip 0.6s ease-in-out forwards;
    }

    .animate-shake {
        animation: shake 0.5s ease-in-out;
    }

    .animate-rubber-band {
        animation: rubberBand 1s ease-in-out;
    }

    .animate-jello {
        animation: jello 1s ease-in-out;
    }

    .animate-tada {
        animation: tada 1s ease-in-out;
    }

    /* Enhanced Hover Effects */
    .hover-tilt {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-tilt:hover {
        transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.05);
    }

    .hover-bounce {
        transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    .hover-bounce:hover {
        transform: scale(1.1) translateY(-5px);
    }

    .hover-rotate {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-rotate:hover {
        transform: rotate(5deg) scale(1.05);
    }

    .hover-slide {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-slide:hover {
        transform: translateX(10px);
    }

    /* Mobile-first responsive utilities */
    .mobile-padding {
        @apply px-4 py-6 sm:px-6 sm:py-8 md:px-8 md:py-10;
    }

    .mobile-margin {
        @apply mx-4 my-6 sm:mx-6 sm:my-8 md:mx-8 md:my-10;
    }

    .mobile-text {
        @apply text-sm sm:text-base md:text-lg;
    }

    .mobile-heading {
        @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
    }

    .mobile-button {
        @apply px-4 py-2 text-sm sm:px-6 sm:py-3 sm:text-base md:px-8 md:py-4 md:text-lg;
    }

    /* New Keyframes */
    @keyframes floatSlow {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes wiggle {
        0%, 7% { transform: rotateZ(0); }
        15% { transform: rotateZ(-15deg); }
        20% { transform: rotateZ(10deg); }
        25% { transform: rotateZ(-10deg); }
        30% { transform: rotateZ(6deg); }
        35% { transform: rotateZ(-4deg); }
        40%, 100% { transform: rotateZ(0); }
    }

    @keyframes heartbeat {
        0% { transform: scale(1); }
        14% { transform: scale(1.3); }
        28% { transform: scale(1); }
        42% { transform: scale(1.3); }
        70% { transform: scale(1); }
    }

    @keyframes slideDown {
        from { transform: translateY(-100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateY(100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes zoomIn {
        from { transform: scale(0); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
    }

    @keyframes zoomOut {
        from { transform: scale(1); opacity: 1; }
        to { transform: scale(0); opacity: 0; }
    }

    @keyframes flip {
        from { transform: perspective(400px) rotateY(0); }
        40% { transform: perspective(400px) rotateY(-180deg); }
        to { transform: perspective(400px) rotateY(-180deg); }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
        20%, 40%, 60%, 80% { transform: translateX(10px); }
    }

    @keyframes rubberBand {
        from { transform: scale3d(1, 1, 1); }
        30% { transform: scale3d(1.25, 0.75, 1); }
        40% { transform: scale3d(0.75, 1.25, 1); }
        50% { transform: scale3d(1.15, 0.85, 1); }
        65% { transform: scale3d(0.95, 1.05, 1); }
        75% { transform: scale3d(1.05, 0.95, 1); }
        to { transform: scale3d(1, 1, 1); }
    }

    @keyframes jello {
        from, 11.1%, to { transform: translate3d(0, 0, 0); }
        22.2% { transform: skewX(-12.5deg) skewY(-12.5deg); }
        33.3% { transform: skewX(6.25deg) skewY(6.25deg); }
        44.4% { transform: skewX(-3.125deg) skewY(-3.125deg); }
        55.5% { transform: skewX(1.5625deg) skewY(1.5625deg); }
        66.6% { transform: skewX(-0.78125deg) skewY(-0.78125deg); }
        77.7% { transform: skewX(0.390625deg) skewY(0.390625deg); }
        88.8% { transform: skewX(-0.1953125deg) skewY(-0.1953125deg); }
    }

    @keyframes tada {
        from { transform: scale3d(1, 1, 1); }
        10%, 20% { transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg); }
        30%, 50%, 70%, 90% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
        40%, 60%, 80% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
        to { transform: scale3d(1, 1, 1); }
    }

    /* Advanced Smooth Animations */
    .animate-smooth-fade-in {
        animation: smoothFadeIn 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-up {
        animation: smoothSlideUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-down {
        animation: smoothSlideDown 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-left {
        animation: smoothSlideLeft 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-right {
        animation: smoothSlideRight 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-scale {
        animation: smoothScale 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    .animate-smooth-rotate {
        animation: smoothRotate 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
    }

    .animate-smooth-bounce {
        animation: smoothBounce 2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    .animate-smooth-pulse {
        animation: smoothPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .animate-smooth-glow {
        animation: smoothGlow 3s ease-in-out infinite alternate;
    }

    .animate-smooth-wave {
        animation: smoothWave 3s ease-in-out infinite;
    }

    .animate-smooth-morph {
        animation: smoothMorph 4s ease-in-out infinite;
    }

    .animate-smooth-levitate {
        animation: smoothLevitate 6s ease-in-out infinite;
    }

    .animate-smooth-breathe {
        animation: smoothBreathe 4s ease-in-out infinite;
    }

    .animate-smooth-shimmer {
        animation: smoothShimmer 2s linear infinite;
    }

    .animate-smooth-typewriter {
        animation: smoothTypewriter 3s steps(40, end) forwards;
    }

    .animate-smooth-reveal {
        animation: smoothReveal 1.5s cubic-bezier(0.77, 0, 0.175, 1) forwards;
    }

    .animate-smooth-elastic {
        animation: smoothElastic 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    }

    .animate-smooth-magnetic {
        animation: smoothMagnetic 2s ease-in-out infinite;
    }

    /* Advanced Hover Effects */
    .hover-smooth-lift {
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform-style: preserve-3d;
    }

    .hover-smooth-lift:hover {
        transform: translateY(-12px) rotateX(15deg) rotateY(5deg) scale(1.05);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    .hover-smooth-glow {
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
    }

    .hover-smooth-glow::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .hover-smooth-glow:hover::before {
        left: 100%;
    }

    .hover-smooth-glow:hover {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.6), 0 0 60px rgba(147, 51, 234, 0.4);
        transform: scale(1.02);
    }

    .hover-smooth-tilt {
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform-style: preserve-3d;
    }

    .hover-smooth-tilt:hover {
        transform: perspective(1000px) rotateX(10deg) rotateY(10deg) translateZ(20px);
    }

    .hover-smooth-magnetic {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        cursor: pointer;
    }

    .hover-smooth-magnetic:hover {
        transform: scale(1.1) rotate(2deg);
        filter: brightness(1.1) saturate(1.2);
    }

    .hover-smooth-ripple {
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .hover-smooth-ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .hover-smooth-ripple:hover::after {
        width: 300px;
        height: 300px;
    }

    /* Smooth Keyframes */
    @keyframes smoothFadeIn {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            filter: blur(5px);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0);
        }
    }

    @keyframes smoothSlideUp {
        from {
            opacity: 0;
            transform: translateY(60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes smoothSlideDown {
        from {
            opacity: 0;
            transform: translateY(-60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes smoothSlideLeft {
        from {
            opacity: 0;
            transform: translateX(60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    @keyframes smoothSlideRight {
        from {
            opacity: 0;
            transform: translateX(-60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    @keyframes smoothScale {
        from {
            opacity: 0;
            transform: scale(0.3) rotate(-10deg);
        }
        50% {
            transform: scale(1.1) rotate(5deg);
        }
        to {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    @keyframes smoothRotate {
        from { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(90deg) scale(1.1); }
        50% { transform: rotate(180deg) scale(1); }
        75% { transform: rotate(270deg) scale(1.1); }
        to { transform: rotate(360deg) scale(1); }
    }

    @keyframes smoothBounce {
        0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0, 0, 0) scale(1);
        }
        40%, 43% {
            transform: translate3d(0, -30px, 0) scale(1.1);
        }
        70% {
            transform: translate3d(0, -15px, 0) scale(1.05);
        }
        90% {
            transform: translate3d(0, -4px, 0) scale(1.02);
        }
    }

    @keyframes smoothPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.05);
        }
    }

    @keyframes smoothGlow {
        from {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
            filter: brightness(1);
        }
        to {
            box-shadow: 0 0 40px rgba(147, 51, 234, 0.8), 0 0 80px rgba(59, 130, 246, 0.3);
            filter: brightness(1.2);
        }
    }

    @keyframes smoothWave {
        0%, 100% { transform: translateY(0) rotate(0deg); }
        25% { transform: translateY(-10px) rotate(1deg); }
        50% { transform: translateY(0) rotate(0deg); }
        75% { transform: translateY(10px) rotate(-1deg); }
    }

    @keyframes smoothMorph {
        0%, 100% { border-radius: 20px; transform: scale(1); }
        25% { border-radius: 50px; transform: scale(1.05); }
        50% { border-radius: 20px; transform: scale(1.1); }
        75% { border-radius: 10px; transform: scale(1.05); }
    }

    @keyframes smoothLevitate {
        0%, 100% {
            transform: translateY(0) rotateX(0deg);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        50% {
            transform: translateY(-20px) rotateX(5deg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
    }

    @keyframes smoothBreathe {
        0%, 100% { transform: scale(1) rotate(0deg); }
        50% { transform: scale(1.03) rotate(1deg); }
    }

    @keyframes smoothShimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    @keyframes smoothTypewriter {
        from { width: 0; }
        to { width: 100%; }
    }

    @keyframes smoothReveal {
        from {
            opacity: 0;
            transform: translateY(100px) scale(0.8);
            clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0% 100%);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
        }
    }

    @keyframes smoothElastic {
        0% {
            transform: scale(0) rotate(-360deg);
            opacity: 0;
        }
        50% {
            transform: scale(1.2) rotate(-180deg);
            opacity: 0.8;
        }
        100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
        }
    }

    @keyframes smoothMagnetic {
        0%, 100% { transform: translate(0, 0) scale(1); }
        25% { transform: translate(2px, -2px) scale(1.01); }
        50% { transform: translate(-1px, 2px) scale(1.02); }
        75% { transform: translate(-2px, -1px) scale(1.01); }
    }

    /* Basic Utility Classes */
    .text-center { text-align: center; }
    .text-left { text-align: left; }
    .text-right { text-align: right; }

    .w-full { width: 100%; }
    .h-full { height: 100%; }

    .hidden { display: none; }
    .block { display: block; }
    .inline-block { display: inline-block; }

    .rounded-xl { border-radius: 0.75rem; }
    .rounded-2xl { border-radius: 1rem; }
    .rounded-full { border-radius: 9999px; }

    .shadow-lg {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .transition-all {
        transition: all 0.2s ease;
    }

    .duration-300 {
        transition-duration: 300ms;
    }

    .hover\:scale-105:hover {
        transform: scale(1.05);
    }

    .gap-2 { gap: 0.5rem; }
    .gap-3 { gap: 0.75rem; }
    .gap-4 { gap: 1rem; }
    .gap-6 { gap: 1.5rem; }
    .gap-8 { gap: 2rem; }

    .p-3 { padding: 0.75rem; }
    .p-4 { padding: 1rem; }
    .p-6 { padding: 1.5rem; }
    .p-8 { padding: 2rem; }

    .max-w-2xl { max-width: 42rem; }
    .max-w-3xl { max-width: 48rem; }
    .max-w-4xl { max-width: 56rem; }

    .mx-auto {
        margin-left: auto;
        margin-right: auto;
    }

    .overflow-hidden { overflow: hidden; }

    .object-cover {
        object-fit: cover;
    }

    .aspect-w-16 {
        position: relative;
        padding-bottom: 75%;
    }

    .aspect-w-16 > * {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .h-48 { height: 12rem; }

    /* Professional Hover Effects */
    .hover-elegant-lift {
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        transform-style: preserve-3d;
        position: relative;
        overflow: hidden;
    }

    .hover-elegant-lift::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        opacity: 0;
        transition: opacity 0.6s ease;
        pointer-events: none;
    }

    .hover-elegant-lift:hover {
        transform: translateY(-16px) rotateX(8deg) rotateY(4deg) scale(1.03);
        box-shadow:
            0 32px 64px rgba(0, 0, 0, 0.25),
            0 16px 32px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    .hover-elegant-lift:hover::before {
        opacity: 1;
    }

    .hover-elegant-glow {
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        overflow: hidden;
    }

    .hover-elegant-glow::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
        opacity: 0;
        transition: opacity 0.6s ease;
        pointer-events: none;
    }

    .hover-elegant-glow:hover::after {
        opacity: 1;
    }

    .hover-elegant-glow:hover {
        box-shadow:
            0 0 40px rgba(59, 130, 246, 0.4),
            0 0 80px rgba(147, 51, 234, 0.2),
            0 0 120px rgba(59, 130, 246, 0.1);
        transform: scale(1.02);
    }

    .hover-elegant-tilt {
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        transform-style: preserve-3d;
    }

    .hover-elegant-tilt:hover {
        transform: perspective(1000px) rotateX(12deg) rotateY(12deg) translateZ(24px);
    }

    .hover-elegant-magnetic {
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        cursor: pointer;
    }

    .hover-elegant-magnetic:hover {
        transform: scale(1.08) rotate(1deg);
        filter: brightness(1.1) saturate(1.3) contrast(1.1);
    }

    .hover-elegant-ripple {
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .hover-elegant-ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        transition: width 0.8s ease, height 0.8s ease;
    }

    .hover-elegant-ripple:hover::after {
        width: 400px;
        height: 400px;
    }

    /* Elegant Keyframes */
    @keyframes elegantEntrance {
        0% {
            opacity: 0;
            transform: translateY(60px) scale(0.8) rotateX(30deg);
            filter: blur(10px);
        }
        50% {
            opacity: 0.8;
            transform: translateY(-10px) scale(1.05) rotateX(-5deg);
            filter: blur(2px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1) rotateX(0deg);
            filter: blur(0);
        }
    }

    @keyframes elegantSlideUp {
        0% {
            opacity: 0;
            transform: translateY(80px) scale(0.9);
            filter: blur(8px);
        }
        60% {
            opacity: 0.9;
            transform: translateY(-8px) scale(1.02);
            filter: blur(1px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0);
        }
    }

    @keyframes elegantFadeIn {
        0% {
            opacity: 0;
            transform: scale(0.85) rotateY(15deg);
            filter: blur(12px) brightness(0.8);
        }
        40% {
            opacity: 0.6;
            transform: scale(1.02) rotateY(-3deg);
            filter: blur(3px) brightness(1.1);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotateY(0deg);
            filter: blur(0) brightness(1);
        }
    }

    @keyframes elegantScale {
        0% {
            opacity: 0;
            transform: scale(0.3) rotate(-15deg);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.15) rotate(5deg);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    @keyframes elegantFloat {
        0%, 100% {
            transform: translateY(0) rotateZ(0deg);
        }
        25% {
            transform: translateY(-12px) rotateZ(1deg);
        }
        50% {
            transform: translateY(0) rotateZ(0deg);
        }
        75% {
            transform: translateY(12px) rotateZ(-1deg);
        }
    }

    @keyframes elegantGlow {
        0% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            filter: brightness(1) saturate(1);
        }
        100% {
            box-shadow:
                0 0 40px rgba(147, 51, 234, 0.6),
                0 0 80px rgba(59, 130, 246, 0.4),
                0 0 120px rgba(147, 51, 234, 0.2);
            filter: brightness(1.2) saturate(1.3);
        }
    }

    @keyframes elegantShimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    @keyframes elegantTypewriter {
        0% { width: 0; border-color: #3B82F6; }
        90% { border-color: #3B82F6; }
        100% { width: 100%; border-color: transparent; }
    }

    @keyframes elegantReveal {
        0% {
            opacity: 0;
            transform: translateY(120px) scale(0.7);
            clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0% 100%);
        }
        50% {
            opacity: 0.8;
            transform: translateY(-20px) scale(1.05);
            clip-path: polygon(0 60%, 100% 60%, 100% 100%, 0% 100%);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
        }
    }

    @keyframes elegantMorph {
        0%, 100% {
            border-radius: 24px;
            transform: scale(1) rotate(0deg);
        }
        25% {
            border-radius: 60px;
            transform: scale(1.02) rotate(1deg);
        }
        50% {
            border-radius: 16px;
            transform: scale(1.05) rotate(0deg);
        }
        75% {
            border-radius: 40px;
            transform: scale(1.02) rotate(-1deg);
        }
    }

    @keyframes elegantPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
            filter: brightness(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.03);
            filter: brightness(1.1);
        }
    }

    @keyframes elegantBounce {
        0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
        }
        40%, 43% {
            transform: translate3d(0, -40px, 0) scale(1.08) rotate(2deg);
        }
        70% {
            transform: translate3d(0, -20px, 0) scale(1.04) rotate(-1deg);
        }
        90% {
            transform: translate3d(0, -8px, 0) scale(1.02) rotate(0.5deg);
        }
    }

    /* Professional Responsive Design System */

    /* Mobile First Approach */
    .container-elegant {
        width: 100%;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    @media (min-width: 640px) {
        .container-elegant { padding: 0 1.5rem; }
    }

    @media (min-width: 768px) {
        .container-elegant { padding: 0 2rem; }
    }

    @media (min-width: 1024px) {
        .container-elegant { padding: 0 2.5rem; }
    }

    @media (min-width: 1280px) {
        .container-elegant { padding: 0 3rem; }
    }

    /* Basic Typography */
    .text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
        font-weight: 800;
    }

    .text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
        font-weight: 700;
    }

    .text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
        font-weight: 600;
    }

    .text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-weight: 600;
    }

    .text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-weight: 500;
    }

    .text-base {
        font-size: 1rem;
        line-height: 1.5rem;
        font-weight: 400;
    }

    .text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
        font-weight: 400;
    }

    .text-xs {
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: 400;
    }

    /* Colors */
    .text-gray-900 { color: #111827; }
    .text-gray-800 { color: #1f2937; }
    .text-gray-700 { color: #374151; }
    .text-gray-600 { color: #4b5563; }
    .text-gray-500 { color: #6b7280; }
    .text-gray-400 { color: #9ca3af; }
    .text-white { color: #ffffff; }

    .dark .text-gray-900 { color: #f9fafb; }
    .dark .text-gray-800 { color: #f3f4f6; }
    .dark .text-gray-700 { color: #e5e7eb; }
    .dark .text-gray-600 { color: #d1d5db; }
    .dark .text-gray-500 { color: #9ca3af; }
    .dark .text-gray-400 { color: #6b7280; }

    /* Basic Layout */
    .container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    @media (min-width: 640px) {
        .container {
            padding: 0 1.5rem;
        }
    }

    @media (min-width: 1024px) {
        .container {
            padding: 0 2rem;
        }
    }

    .py-20 {
        padding-top: 5rem;
        padding-bottom: 5rem;
    }

    .py-16 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .py-12 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .mb-16 { margin-bottom: 4rem; }
    .mb-12 { margin-bottom: 3rem; }
    .mb-8 { margin-bottom: 2rem; }
    .mb-6 { margin-bottom: 1.5rem; }
    .mb-4 { margin-bottom: 1rem; }
    .mb-3 { margin-bottom: 0.75rem; }
    .mb-2 { margin-bottom: 0.5rem; }

    .mt-16 { margin-top: 4rem; }
    .mt-12 { margin-top: 3rem; }
    .mt-8 { margin-top: 2rem; }

    /* Grid */
    .grid {
        display: grid;
        gap: 1.5rem;
    }

    .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
    .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

    @media (max-width: 640px) {
        .grid-cols-2,
        .grid-cols-3,
        .grid-cols-4 {
            grid-template-columns: 1fr;
        }
    }

    @media (min-width: 641px) and (max-width: 768px) {
        .grid-cols-3,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (min-width: 769px) and (max-width: 1024px) {
        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* Flex */
    .flex {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .flex-col {
        flex-direction: column;
    }

    .justify-center {
        justify-content: center;
    }

    .justify-between {
        justify-content: space-between;
    }

    .items-center {
        align-items: center;
    }

    .text-center {
        text-align: center;
    }

    /* Enhanced Mobile-First Responsive */
    @media (max-width: 480px) {
        .hero-title {
            font-size: clamp(2rem, 10vw, 3rem);
            line-height: 1.1;
            text-align: center;
        }

        .section-title {
            font-size: clamp(1.5rem, 8vw, 2.5rem);
            text-align: center;
        }

        .glass-card {
            padding: clamp(1rem, 5vw, 1.5rem);
            border-radius: 1rem;
            margin-bottom: 1rem;
        }

        .btn-primary,
        .btn-secondary {
            width: 100%;
            justify-content: center;
            padding: 1rem 1.5rem;
            font-size: 1rem;
        }

        .content-grid {
            gap: 1rem;
        }

        .flex-container {
            flex-direction: column;
            gap: 1rem;
        }

        .container-fluid {
            padding: 0 1rem;
        }

        .section-wrapper {
            padding: 2rem 0;
        }
    }

    @media (min-width: 481px) and (max-width: 768px) {
        .hero-title {
            font-size: clamp(2.5rem, 8vw, 4rem);
        }

        .glass-card {
            padding: clamp(1.5rem, 4vw, 2rem);
        }

        .content-grid.cols-2 {
            grid-template-columns: 1fr;
        }

        .content-grid.cols-3,
        .content-grid.cols-4 {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (min-width: 769px) and (max-width: 1024px) {
        .content-grid.cols-4 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (min-width: 1025px) {
        .glass-card:hover {
            transform: translateY(-12px) scale(1.03);
        }

        .btn-primary:hover {
            transform: translateY(-4px) scale(1.05);
        }
    }

    /* Responsive Spacing */
    .spacing-elegant-xs { padding: 0.5rem; }
    .spacing-elegant-sm { padding: 0.75rem; }
    .spacing-elegant-md { padding: 1rem; }
    .spacing-elegant-lg { padding: 1.5rem; }
    .spacing-elegant-xl { padding: 2rem; }

    @media (min-width: 640px) {
        .spacing-elegant-xs { padding: 0.75rem; }
        .spacing-elegant-sm { padding: 1rem; }
        .spacing-elegant-md { padding: 1.5rem; }
        .spacing-elegant-lg { padding: 2rem; }
        .spacing-elegant-xl { padding: 3rem; }
    }

    @media (min-width: 768px) {
        .spacing-elegant-xs { padding: 1rem; }
        .spacing-elegant-sm { padding: 1.5rem; }
        .spacing-elegant-md { padding: 2rem; }
        .spacing-elegant-lg { padding: 3rem; }
        .spacing-elegant-xl { padding: 4rem; }
    }

    @media (min-width: 1024px) {
        .spacing-elegant-xs { padding: 1.25rem; }
        .spacing-elegant-sm { padding: 2rem; }
        .spacing-elegant-md { padding: 2.5rem; }
        .spacing-elegant-lg { padding: 4rem; }
        .spacing-elegant-xl { padding: 5rem; }
    }

    /* Basic Card System */
    .bg-white { background-color: #ffffff; }
    .bg-gray-50 { background-color: #f9fafb; }
    .bg-gray-100 { background-color: #f3f4f6; }
    .bg-gray-800 { background-color: #1f2937; }
    .bg-gray-900 { background-color: #111827; }

    .dark .bg-white { background-color: #1f2937; }
    .dark .bg-gray-50 { background-color: #111827; }
    .dark .bg-gray-100 { background-color: #374151; }

    .bg-gradient-to-r {
        background-image: linear-gradient(to right, var(--tw-gradient-stops));
    }

    .bg-gradient-to-br {
        background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
    }

    .from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }
    .to-purple-500 { --tw-gradient-to: #8b5cf6; }
    .from-green-500 { --tw-gradient-from: #10b981; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(16, 185, 129, 0)); }
    .to-teal-500 { --tw-gradient-to: #14b8a6; }
    .from-pink-500 { --tw-gradient-from: #ec4899; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 72, 153, 0)); }
    .to-rose-500 { --tw-gradient-to: #f43f5e; }
    .from-blue-400 { --tw-gradient-from: #60a5fa; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0)); }
    .to-purple-400 { --tw-gradient-to: #a78bfa; }
    .from-gray-50 { --tw-gradient-from: #f9fafb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0)); }
    .to-white { --tw-gradient-to: #ffffff; }
    .from-gray-900 { --tw-gradient-from: #111827; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(17, 24, 39, 0)); }
    .to-gray-800 { --tw-gradient-to: #1f2937; }

    .bg-clip-text {
        -webkit-background-clip: text;
        background-clip: text;
    }

    .text-transparent {
        color: transparent;
    }

    .dark .card-elegant {
        background: rgba(15, 23, 42, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.08);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.4),
            0 4px 16px rgba(0, 0, 0, 0.3),
            0 1px 4px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .card-elegant::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.08) 0%,
            rgba(147, 51, 234, 0.04) 30%,
            rgba(236, 72, 153, 0.06) 60%,
            rgba(59, 130, 246, 0.08) 100%);
        background-size: 200% 200%;
        opacity: 0;
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        pointer-events: none;
        animation: gradient-shift 8s ease-in-out infinite;
    }

    .card-elegant::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1px;
        background: linear-gradient(135deg,
            rgba(255,255,255,0.2) 0%,
            rgba(255,255,255,0.05) 50%,
            rgba(255,255,255,0.1) 100%);
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .card-elegant:hover::before {
        opacity: 1;
        background-position: 100% 100%;
    }

    .card-elegant:hover::after {
        opacity: 1;
    }

    .card-elegant:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 10px 30px rgba(0, 0, 0, 0.1),
            0 4px 12px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .dark .card-elegant:hover {
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.6),
            0 10px 30px rgba(0, 0, 0, 0.4),
            0 4px 12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15);
    }

    /* Card Variants */
    .card-elegant-compact {
        padding: clamp(0.75rem, 2vw + 0.25rem, 1.5rem);
        border-radius: clamp(12px, 2vw + 2px, 20px);
    }

    .card-elegant-spacious {
        padding: clamp(1.5rem, 4vw + 1rem, 3.5rem);
        border-radius: clamp(20px, 4vw + 8px, 40px);
    }

    .card-elegant-flat {
        box-shadow:
            0 1px 3px rgba(0, 0, 0, 0.1),
            0 1px 2px rgba(0, 0, 0, 0.06);
    }

    .card-elegant-elevated {
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 12px 25px rgba(0, 0, 0, 0.1),
            0 6px 12px rgba(0, 0, 0, 0.05);
    }

    /* Professional Button System */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 1;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 44px;
        white-space: nowrap;
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
    }

    .btn-primary:hover {
        background: #2563eb;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
    }

    @media (min-width: 768px) {
        .btn {
            padding: 0.875rem 2rem;
            font-size: 1rem;
        }
    }

    .btn-responsive {
        width: 100%;
        justify-content: center;
    }

    @media (min-width: 640px) {
        .btn-responsive {
            width: auto;
        }
    }

    .btn-elegant-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .btn-elegant-primary::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1px;
        background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask-composite: xor;
        pointer-events: none;
    }

    .btn-elegant-primary:hover::before {
        left: 100%;
    }

    .btn-primary:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 8px 30px rgba(59, 130, 246, 0.4),
            0 4px 15px rgba(139, 92, 246, 0.3);
    }

    .btn-primary:active {
        transform: translateY(-1px) scale(1.01);
        transition: all 0.1s ease;
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.08);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        color: inherit;
        font-weight: 600;
        font-size: clamp(0.875rem, 2vw, 1rem);
        padding: clamp(0.875rem, 2.5vw, 1rem) clamp(1.5rem, 4vw, 2.5rem);
        border-radius: clamp(0.75rem, 2vw, 1.25rem);
        border: 1px solid rgba(255, 255, 255, 0.15);
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: clamp(0.5rem, 1.5vw, 0.75rem);
        min-height: clamp(48px, 6vw, 56px);
        white-space: nowrap;
        position: relative;
        overflow: hidden;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(255, 255, 255, 0.25);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .btn-elegant-secondary::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        opacity: 0;
        transition: opacity 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        border-radius: inherit;
    }

    .btn-elegant-secondary:hover::before {
        opacity: 1;
    }

    .btn-elegant-secondary:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-6px) scale(1.03);
        box-shadow:
            0 20px 50px rgba(0, 0, 0, 0.15),
            0 10px 25px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.25);
    }

    .btn-elegant-secondary:active {
        transform: translateY(-2px) scale(1.01);
        transition: all 0.1s cubic-bezier(0.23, 1, 0.32, 1);
    }

    /* Button Variants */
    .btn-elegant-small {
        font-size: clamp(0.75rem, 0.8vw + 0.2rem, 1rem);
        padding: clamp(0.5rem, 1vw + 0.125rem, 0.875rem) clamp(1rem, 2vw + 0.25rem, 1.75rem);
        min-height: clamp(36px, 5vw + 4px, 44px);
    }

    .btn-elegant-large {
        font-size: clamp(1rem, 1.2vw + 0.3rem, 1.375rem);
        padding: clamp(1rem, 2vw + 0.5rem, 1.5rem) clamp(2rem, 4vw + 1rem, 3rem);
        min-height: clamp(52px, 7vw + 12px, 68px);
    }

    .btn-elegant-icon-only {
        padding: clamp(0.75rem, 1.5vw + 0.25rem, 1.25rem);
        aspect-ratio: 1;
        border-radius: 50%;
    }

    /* Mobile Optimizations */
    @media (max-width: 639px) {
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in,
        .animate-elegant-scale,
        .animate-elegant-reveal {
            animation-duration: 0.8s;
        }

        .hover-elegant-lift:hover,
        .hover-elegant-glow:hover,
        .hover-elegant-tilt:hover {
            transform: scale(1.02);
        }

        .card-elegant {
            border-radius: 16px;
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            width: 100%;
            justify-content: center;
        }
    }

    /* Touch Device Optimizations */
    @media (hover: none) and (pointer: coarse) {
        .hover-elegant-lift:hover,
        .hover-elegant-glow:hover,
        .hover-elegant-tilt:hover,
        .hover-elegant-magnetic:hover {
            transform: none;
            box-shadow: inherit;
        }

        .hover-elegant-lift:active,
        .hover-elegant-glow:active,
        .hover-elegant-tilt:active {
            transform: scale(0.98);
        }
    }

    /* High DPI Display Optimizations */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .card-elegant {
            border-width: 0.5px;
        }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in,
        .animate-elegant-scale,
        .animate-elegant-float,
        .animate-elegant-glow,
        .animate-elegant-shimmer,
        .animate-elegant-reveal,
        .animate-elegant-morph,
        .animate-elegant-pulse,
        .animate-elegant-bounce {
            animation: none;
        }

        .hover-elegant-lift,
        .hover-elegant-glow,
        .hover-elegant-tilt,
        .hover-elegant-magnetic,
        .hover-elegant-ripple {
            transition: none;
        }
    }

    /* Ultra-Mobile Optimizations (320px - 480px) */
    @media (max-width: 480px) {
        .container-elegant {
            padding: 0 clamp(0.75rem, 2vw, 1rem);
            max-width: 100%;
        }

        .card-elegant {
            margin-bottom: clamp(0.75rem, 2vw, 1rem);
            border-radius: clamp(12px, 3vw, 16px);
            padding: clamp(1rem, 4vw, 1.5rem);
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            width: 100%;
            justify-content: center;
            font-size: clamp(0.875rem, 2.5vw, 1rem);
            padding: clamp(0.875rem, 3vw, 1rem) clamp(1.25rem, 4vw, 1.5rem);
            border-radius: clamp(10px, 2.5vw, 14px);
        }

        .btn-elegant-small {
            padding: clamp(0.625rem, 2.5vw, 0.75rem) clamp(1rem, 3vw, 1.25rem);
            font-size: clamp(0.75rem, 2vw, 0.875rem);
        }

        /* Mobile-specific animations */
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in {
            animation-duration: 0.6s;
        }

        .hover-elegant-lift:hover,
        .hover-elegant-glow:hover {
            transform: scale(1.01);
        }

        /* Touch-friendly spacing */
        .spacing-elegant-xs { padding: clamp(0.5rem, 2vw, 0.75rem); }
        .spacing-elegant-sm { padding: clamp(0.75rem, 3vw, 1rem); }
        .spacing-elegant-md { padding: clamp(1rem, 4vw, 1.25rem); }
        .spacing-elegant-lg { padding: clamp(1.25rem, 5vw, 1.5rem); }
        .spacing-elegant-xl { padding: clamp(1.5rem, 6vw, 2rem); }
    }

    /* Small Mobile Optimizations (481px - 640px) */
    @media (min-width: 481px) and (max-width: 640px) {
        .container-elegant {
            padding: 0 clamp(1rem, 2.5vw, 1.5rem);
        }

        .card-elegant {
            padding: clamp(1.25rem, 3.5vw, 1.75rem);
            border-radius: clamp(16px, 3vw, 20px);
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            min-width: clamp(120px, 25vw, 160px);
            padding: clamp(0.875rem, 2.5vw, 1.125rem) clamp(1.5rem, 4vw, 2rem);
        }
    }

    /* Tablet Optimizations */
    @media (min-width: 481px) and (max-width: 768px) {
        .container-elegant {
            padding: 0 1.25rem;
        }

        .text-elegant-display {
            font-size: 2.25rem;
        }

        .text-elegant-title {
            font-size: 1.5rem;
        }

        .spacing-elegant-md { padding: 1.5rem; }
        .spacing-elegant-lg { padding: 2rem; }
        .spacing-elegant-xl { padding: 2.5rem; }
    }

    /* Desktop Enhancements */
    @media (min-width: 1024px) {
        .hover-elegant-lift:hover {
            transform: translateY(-20px) rotateX(10deg) rotateY(6deg) scale(1.04);
        }

        .hover-elegant-glow:hover {
            box-shadow:
                0 0 50px rgba(59, 130, 246, 0.5),
                0 0 100px rgba(147, 51, 234, 0.3),
                0 0 150px rgba(59, 130, 246, 0.2);
        }

        .card-elegant:hover {
            transform: translateY(-8px) scale(1.02);
        }
    }

    /* Ultra-wide Screen Optimizations */
    @media (min-width: 1440px) {
        .container-elegant {
            max-width: 1600px;
            padding: 0 4rem;
        }

        .text-elegant-display {
            font-size: 6rem;
        }

        .text-elegant-title {
            font-size: 3rem;
        }

        .spacing-elegant-xl { padding: 6rem; }
    }

    /* Print Styles */
    @media print {
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in,
        .animate-elegant-scale,
        .animate-elegant-float,
        .animate-elegant-glow,
        .animate-elegant-shimmer,
        .animate-elegant-reveal,
        .animate-elegant-morph,
        .animate-elegant-pulse,
        .animate-elegant-bounce {
            animation: none;
        }

        .card-elegant {
            box-shadow: none;
            border: 1px solid #e5e7eb;
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
    }

    /* High Contrast Mode */
    @media (prefers-contrast: high) {
        .card-elegant {
            border: 2px solid;
            background: white;
        }

        .dark .card-elegant {
            background: black;
            border-color: white;
        }

        .btn-elegant-primary {
            background: black;
            color: white;
            border: 2px solid white;
        }

        .btn-elegant-secondary {
            background: white;
            color: black;
            border: 2px solid black;
        }
    }

    /* Focus Visible Enhancements */
    .btn-elegant-primary:focus-visible,
    .btn-elegant-secondary:focus-visible,
    .card-elegant:focus-visible {
        outline: 3px solid #3B82F6;
        outline-offset: 2px;
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
    }

    .dark .loading-skeleton {
        background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
        background-size: 200% 100%;
    }

    @keyframes loading-shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    /* Scroll Snap */
    .scroll-snap-container {
        scroll-snap-type: y mandatory;
        overflow-y: scroll;
    }

    .scroll-snap-item {
        scroll-snap-align: start;
    }

    /* Custom Scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #3B82F6, #8B5CF6);
        border-radius: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #2563EB, #7C3AED);
    }
}
