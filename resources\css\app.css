@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import 'aos/dist/aos.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Inter', sans-serif;
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100 dark:bg-gray-800;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-400 dark:bg-gray-600 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-500 dark:bg-gray-500;
    }
}

@layer components {
    /* Glass morphism components */
    .glass {
        -webkit-backdrop-filter: blur(16px);
        backdrop-filter: blur(16px);
    }

    .glass-card {
        @apply backdrop-blur-md bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 rounded-xl;
    }

    .glass-button {
        @apply glass backdrop-blur-md bg-white/20 dark:bg-black/20 border border-white/30 dark:border-white/20 hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-300;
    }

    /* Neumorphism components */
    .neuro-card {
        @apply bg-gray-100 dark:bg-gray-800 rounded-2xl shadow-neumorphism dark:shadow-none;
    }

    .neuro-button {
        @apply bg-gray-100 dark:bg-gray-800 rounded-xl shadow-neumorphism dark:shadow-none hover:shadow-neumorphism-inset dark:hover:shadow-none transition-all duration-300;
    }

    /* Gradient backgrounds */
    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-bg-2 {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .gradient-bg-3 {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .gradient-bg-4 {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    /* Animated gradients */
    .animated-gradient {
        background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
    }

    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Button styles */
    .btn-primary {
        @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    .btn-secondary {
        @apply bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    .btn-accent {
        @apply bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    /* Card styles */
    .card {
        @apply bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-200 dark:border-gray-700;
    }

    /* Line clamp utility */
    .line-clamp-2 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    .card-glass {
        @apply glass-card hover:bg-white/20 dark:hover:bg-black/20 transform hover:scale-105 transition-all duration-300;
    }

    /* Input styles */
    .input-modern {
        @apply w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-300;
    }

    /* Loading animations */
    .skeleton {
        @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
    }

    .loading-dots {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 80px;
    }

    .loading-dots div {
        position: absolute;
        top: 33px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: #3b82f6;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }

    .loading-dots div:nth-child(1) {
        left: 8px;
        animation: loading-dots1 0.6s infinite;
    }

    .loading-dots div:nth-child(2) {
        left: 8px;
        animation: loading-dots2 0.6s infinite;
    }

    .loading-dots div:nth-child(3) {
        left: 32px;
        animation: loading-dots2 0.6s infinite;
    }

    .loading-dots div:nth-child(4) {
        left: 56px;
        animation: loading-dots3 0.6s infinite;
    }

    @keyframes loading-dots1 {
        0% { transform: scale(0); }
        100% { transform: scale(1); }
    }

    @keyframes loading-dots3 {
        0% { transform: scale(1); }
        100% { transform: scale(0); }
    }

    @keyframes loading-dots2 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(24px, 0); }
    }

    /* Advanced Animations */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    @keyframes pulse-glow {
        0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
        50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
    }

    @keyframes slide-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slide-in-left {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slide-in-right {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes scale-in {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }

    /* Animation Classes */
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }

    .animate-pulse-glow {
        animation: pulse-glow 2s ease-in-out infinite;
    }

    .animate-slide-in-up {
        animation: slide-in-up 0.6s ease-out;
    }

    .animate-slide-in-left {
        animation: slide-in-left 0.6s ease-out;
    }

    .animate-slide-in-right {
        animation: slide-in-right 0.6s ease-out;
    }

    .animate-fade-in {
        animation: fade-in 0.8s ease-out;
    }

    .animate-scale-in {
        animation: scale-in 0.5s ease-out;
    }

    .animate-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }

    /* Hover Effects */
    .hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-lift:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .hover-glow:hover {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
    }

    /* Glass Card Enhanced */
    .glass-card-enhanced {
        background: rgba(255, 255, 255, 0.1);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    /* Smooth Transitions */
    .transition-smooth {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .transition-bounce {
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* Image Optimization */
    .destination-image {
        object-fit: cover;
        object-position: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    .destination-image:hover {
        transform: scale(1.05);
        filter: brightness(1.1) contrast(1.05);
    }

    /* Image Loading States */
    .image-loading {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Smooth Image Reveal */
    .image-reveal {
        opacity: 0;
        transition: opacity 0.6s ease-in-out;
    }

    .image-reveal.loaded {
        opacity: 1;
    }

    /* Aspect Ratio Containers */
    .aspect-ratio-4-3 {
        aspect-ratio: 4/3;
    }

    .aspect-ratio-16-9 {
        aspect-ratio: 16/9;
    }

    .aspect-ratio-3-2 {
        aspect-ratio: 3/2;
    }

    /* New Enhanced Animations */
    .animate-float-slow {
        animation: floatSlow 6s ease-in-out infinite;
    }

    .animate-count-up {
        animation: countUp 2s ease-out forwards;
    }

    .animate-wiggle {
        animation: wiggle 1s ease-in-out infinite;
    }

    .animate-heartbeat {
        animation: heartbeat 1.5s ease-in-out infinite;
    }

    .animate-slide-down {
        animation: slideDown 0.5s ease-out forwards;
    }

    .animate-slide-up {
        animation: slideUp 0.5s ease-out forwards;
    }

    .animate-zoom-in {
        animation: zoomIn 0.5s ease-out forwards;
    }

    .animate-zoom-out {
        animation: zoomOut 0.5s ease-out forwards;
    }

    .animate-flip {
        animation: flip 0.6s ease-in-out forwards;
    }

    .animate-shake {
        animation: shake 0.5s ease-in-out;
    }

    .animate-rubber-band {
        animation: rubberBand 1s ease-in-out;
    }

    .animate-jello {
        animation: jello 1s ease-in-out;
    }

    .animate-tada {
        animation: tada 1s ease-in-out;
    }

    /* Enhanced Hover Effects */
    .hover-tilt {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-tilt:hover {
        transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.05);
    }

    .hover-bounce {
        transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    .hover-bounce:hover {
        transform: scale(1.1) translateY(-5px);
    }

    .hover-rotate {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-rotate:hover {
        transform: rotate(5deg) scale(1.05);
    }

    .hover-slide {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-slide:hover {
        transform: translateX(10px);
    }

    /* Mobile-first responsive utilities */
    .mobile-padding {
        @apply px-4 py-6 sm:px-6 sm:py-8 md:px-8 md:py-10;
    }

    .mobile-margin {
        @apply mx-4 my-6 sm:mx-6 sm:my-8 md:mx-8 md:my-10;
    }

    .mobile-text {
        @apply text-sm sm:text-base md:text-lg;
    }

    .mobile-heading {
        @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
    }

    .mobile-button {
        @apply px-4 py-2 text-sm sm:px-6 sm:py-3 sm:text-base md:px-8 md:py-4 md:text-lg;
    }

    /* New Keyframes */
    @keyframes floatSlow {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes wiggle {
        0%, 7% { transform: rotateZ(0); }
        15% { transform: rotateZ(-15deg); }
        20% { transform: rotateZ(10deg); }
        25% { transform: rotateZ(-10deg); }
        30% { transform: rotateZ(6deg); }
        35% { transform: rotateZ(-4deg); }
        40%, 100% { transform: rotateZ(0); }
    }

    @keyframes heartbeat {
        0% { transform: scale(1); }
        14% { transform: scale(1.3); }
        28% { transform: scale(1); }
        42% { transform: scale(1.3); }
        70% { transform: scale(1); }
    }

    @keyframes slideDown {
        from { transform: translateY(-100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateY(100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes zoomIn {
        from { transform: scale(0); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
    }

    @keyframes zoomOut {
        from { transform: scale(1); opacity: 1; }
        to { transform: scale(0); opacity: 0; }
    }

    @keyframes flip {
        from { transform: perspective(400px) rotateY(0); }
        40% { transform: perspective(400px) rotateY(-180deg); }
        to { transform: perspective(400px) rotateY(-180deg); }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
        20%, 40%, 60%, 80% { transform: translateX(10px); }
    }

    @keyframes rubberBand {
        from { transform: scale3d(1, 1, 1); }
        30% { transform: scale3d(1.25, 0.75, 1); }
        40% { transform: scale3d(0.75, 1.25, 1); }
        50% { transform: scale3d(1.15, 0.85, 1); }
        65% { transform: scale3d(0.95, 1.05, 1); }
        75% { transform: scale3d(1.05, 0.95, 1); }
        to { transform: scale3d(1, 1, 1); }
    }

    @keyframes jello {
        from, 11.1%, to { transform: translate3d(0, 0, 0); }
        22.2% { transform: skewX(-12.5deg) skewY(-12.5deg); }
        33.3% { transform: skewX(6.25deg) skewY(6.25deg); }
        44.4% { transform: skewX(-3.125deg) skewY(-3.125deg); }
        55.5% { transform: skewX(1.5625deg) skewY(1.5625deg); }
        66.6% { transform: skewX(-0.78125deg) skewY(-0.78125deg); }
        77.7% { transform: skewX(0.390625deg) skewY(0.390625deg); }
        88.8% { transform: skewX(-0.1953125deg) skewY(-0.1953125deg); }
    }

    @keyframes tada {
        from { transform: scale3d(1, 1, 1); }
        10%, 20% { transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg); }
        30%, 50%, 70%, 90% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
        40%, 60%, 80% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
        to { transform: scale3d(1, 1, 1); }
    }

    /* Advanced Smooth Animations */
    .animate-smooth-fade-in {
        animation: smoothFadeIn 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-up {
        animation: smoothSlideUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-down {
        animation: smoothSlideDown 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-left {
        animation: smoothSlideLeft 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-slide-right {
        animation: smoothSlideRight 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .animate-smooth-scale {
        animation: smoothScale 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    .animate-smooth-rotate {
        animation: smoothRotate 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
    }

    .animate-smooth-bounce {
        animation: smoothBounce 2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    .animate-smooth-pulse {
        animation: smoothPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .animate-smooth-glow {
        animation: smoothGlow 3s ease-in-out infinite alternate;
    }

    .animate-smooth-wave {
        animation: smoothWave 3s ease-in-out infinite;
    }

    .animate-smooth-morph {
        animation: smoothMorph 4s ease-in-out infinite;
    }

    .animate-smooth-levitate {
        animation: smoothLevitate 6s ease-in-out infinite;
    }

    .animate-smooth-breathe {
        animation: smoothBreathe 4s ease-in-out infinite;
    }

    .animate-smooth-shimmer {
        animation: smoothShimmer 2s linear infinite;
    }

    .animate-smooth-typewriter {
        animation: smoothTypewriter 3s steps(40, end) forwards;
    }

    .animate-smooth-reveal {
        animation: smoothReveal 1.5s cubic-bezier(0.77, 0, 0.175, 1) forwards;
    }

    .animate-smooth-elastic {
        animation: smoothElastic 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    }

    .animate-smooth-magnetic {
        animation: smoothMagnetic 2s ease-in-out infinite;
    }

    /* Advanced Hover Effects */
    .hover-smooth-lift {
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform-style: preserve-3d;
    }

    .hover-smooth-lift:hover {
        transform: translateY(-12px) rotateX(15deg) rotateY(5deg) scale(1.05);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    .hover-smooth-glow {
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
    }

    .hover-smooth-glow::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .hover-smooth-glow:hover::before {
        left: 100%;
    }

    .hover-smooth-glow:hover {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.6), 0 0 60px rgba(147, 51, 234, 0.4);
        transform: scale(1.02);
    }

    .hover-smooth-tilt {
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform-style: preserve-3d;
    }

    .hover-smooth-tilt:hover {
        transform: perspective(1000px) rotateX(10deg) rotateY(10deg) translateZ(20px);
    }

    .hover-smooth-magnetic {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        cursor: pointer;
    }

    .hover-smooth-magnetic:hover {
        transform: scale(1.1) rotate(2deg);
        filter: brightness(1.1) saturate(1.2);
    }

    .hover-smooth-ripple {
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .hover-smooth-ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .hover-smooth-ripple:hover::after {
        width: 300px;
        height: 300px;
    }

    /* Smooth Keyframes */
    @keyframes smoothFadeIn {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            filter: blur(5px);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0);
        }
    }

    @keyframes smoothSlideUp {
        from {
            opacity: 0;
            transform: translateY(60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes smoothSlideDown {
        from {
            opacity: 0;
            transform: translateY(-60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes smoothSlideLeft {
        from {
            opacity: 0;
            transform: translateX(60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    @keyframes smoothSlideRight {
        from {
            opacity: 0;
            transform: translateX(-60px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    @keyframes smoothScale {
        from {
            opacity: 0;
            transform: scale(0.3) rotate(-10deg);
        }
        50% {
            transform: scale(1.1) rotate(5deg);
        }
        to {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    @keyframes smoothRotate {
        from { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(90deg) scale(1.1); }
        50% { transform: rotate(180deg) scale(1); }
        75% { transform: rotate(270deg) scale(1.1); }
        to { transform: rotate(360deg) scale(1); }
    }

    @keyframes smoothBounce {
        0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0, 0, 0) scale(1);
        }
        40%, 43% {
            transform: translate3d(0, -30px, 0) scale(1.1);
        }
        70% {
            transform: translate3d(0, -15px, 0) scale(1.05);
        }
        90% {
            transform: translate3d(0, -4px, 0) scale(1.02);
        }
    }

    @keyframes smoothPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.05);
        }
    }

    @keyframes smoothGlow {
        from {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
            filter: brightness(1);
        }
        to {
            box-shadow: 0 0 40px rgba(147, 51, 234, 0.8), 0 0 80px rgba(59, 130, 246, 0.3);
            filter: brightness(1.2);
        }
    }

    @keyframes smoothWave {
        0%, 100% { transform: translateY(0) rotate(0deg); }
        25% { transform: translateY(-10px) rotate(1deg); }
        50% { transform: translateY(0) rotate(0deg); }
        75% { transform: translateY(10px) rotate(-1deg); }
    }

    @keyframes smoothMorph {
        0%, 100% { border-radius: 20px; transform: scale(1); }
        25% { border-radius: 50px; transform: scale(1.05); }
        50% { border-radius: 20px; transform: scale(1.1); }
        75% { border-radius: 10px; transform: scale(1.05); }
    }

    @keyframes smoothLevitate {
        0%, 100% {
            transform: translateY(0) rotateX(0deg);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        50% {
            transform: translateY(-20px) rotateX(5deg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
    }

    @keyframes smoothBreathe {
        0%, 100% { transform: scale(1) rotate(0deg); }
        50% { transform: scale(1.03) rotate(1deg); }
    }

    @keyframes smoothShimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    @keyframes smoothTypewriter {
        from { width: 0; }
        to { width: 100%; }
    }

    @keyframes smoothReveal {
        from {
            opacity: 0;
            transform: translateY(100px) scale(0.8);
            clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0% 100%);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
        }
    }

    @keyframes smoothElastic {
        0% {
            transform: scale(0) rotate(-360deg);
            opacity: 0;
        }
        50% {
            transform: scale(1.2) rotate(-180deg);
            opacity: 0.8;
        }
        100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
        }
    }

    @keyframes smoothMagnetic {
        0%, 100% { transform: translate(0, 0) scale(1); }
        25% { transform: translate(2px, -2px) scale(1.01); }
        50% { transform: translate(-1px, 2px) scale(1.02); }
        75% { transform: translate(-2px, -1px) scale(1.01); }
    }

    /* Professional Elegant Animations */
    .animate-elegant-entrance {
        animation: elegantEntrance 1.5s cubic-bezier(0.23, 1, 0.32, 1) forwards;
    }

    .animate-elegant-slide-up {
        animation: elegantSlideUp 1.2s cubic-bezier(0.23, 1, 0.32, 1) forwards;
    }

    .animate-elegant-fade-in {
        animation: elegantFadeIn 1.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;
    }

    .animate-elegant-scale {
        animation: elegantScale 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    }

    .animate-elegant-float {
        animation: elegantFloat 6s ease-in-out infinite;
    }

    .animate-elegant-glow {
        animation: elegantGlow 4s ease-in-out infinite alternate;
    }

    .animate-elegant-shimmer {
        animation: elegantShimmer 3s linear infinite;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        background-size: 200% 100%;
    }

    .animate-elegant-typewriter {
        animation: elegantTypewriter 4s steps(50, end) forwards;
        overflow: hidden;
        white-space: nowrap;
        border-right: 2px solid;
        border-color: transparent;
    }

    .animate-elegant-reveal {
        animation: elegantReveal 2s cubic-bezier(0.77, 0, 0.175, 1) forwards;
    }

    .animate-elegant-morph {
        animation: elegantMorph 8s ease-in-out infinite;
    }

    .animate-elegant-pulse {
        animation: elegantPulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .animate-elegant-bounce {
        animation: elegantBounce 2.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    /* Professional Hover Effects */
    .hover-elegant-lift {
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        transform-style: preserve-3d;
        position: relative;
        overflow: hidden;
    }

    .hover-elegant-lift::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        opacity: 0;
        transition: opacity 0.6s ease;
        pointer-events: none;
    }

    .hover-elegant-lift:hover {
        transform: translateY(-16px) rotateX(8deg) rotateY(4deg) scale(1.03);
        box-shadow:
            0 32px 64px rgba(0, 0, 0, 0.25),
            0 16px 32px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    .hover-elegant-lift:hover::before {
        opacity: 1;
    }

    .hover-elegant-glow {
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        overflow: hidden;
    }

    .hover-elegant-glow::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
        opacity: 0;
        transition: opacity 0.6s ease;
        pointer-events: none;
    }

    .hover-elegant-glow:hover::after {
        opacity: 1;
    }

    .hover-elegant-glow:hover {
        box-shadow:
            0 0 40px rgba(59, 130, 246, 0.4),
            0 0 80px rgba(147, 51, 234, 0.2),
            0 0 120px rgba(59, 130, 246, 0.1);
        transform: scale(1.02);
    }

    .hover-elegant-tilt {
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        transform-style: preserve-3d;
    }

    .hover-elegant-tilt:hover {
        transform: perspective(1000px) rotateX(12deg) rotateY(12deg) translateZ(24px);
    }

    .hover-elegant-magnetic {
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        cursor: pointer;
    }

    .hover-elegant-magnetic:hover {
        transform: scale(1.08) rotate(1deg);
        filter: brightness(1.1) saturate(1.3) contrast(1.1);
    }

    .hover-elegant-ripple {
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .hover-elegant-ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        transition: width 0.8s ease, height 0.8s ease;
    }

    .hover-elegant-ripple:hover::after {
        width: 400px;
        height: 400px;
    }

    /* Elegant Keyframes */
    @keyframes elegantEntrance {
        0% {
            opacity: 0;
            transform: translateY(60px) scale(0.8) rotateX(30deg);
            filter: blur(10px);
        }
        50% {
            opacity: 0.8;
            transform: translateY(-10px) scale(1.05) rotateX(-5deg);
            filter: blur(2px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1) rotateX(0deg);
            filter: blur(0);
        }
    }

    @keyframes elegantSlideUp {
        0% {
            opacity: 0;
            transform: translateY(80px) scale(0.9);
            filter: blur(8px);
        }
        60% {
            opacity: 0.9;
            transform: translateY(-8px) scale(1.02);
            filter: blur(1px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0);
        }
    }

    @keyframes elegantFadeIn {
        0% {
            opacity: 0;
            transform: scale(0.85) rotateY(15deg);
            filter: blur(12px) brightness(0.8);
        }
        40% {
            opacity: 0.6;
            transform: scale(1.02) rotateY(-3deg);
            filter: blur(3px) brightness(1.1);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotateY(0deg);
            filter: blur(0) brightness(1);
        }
    }

    @keyframes elegantScale {
        0% {
            opacity: 0;
            transform: scale(0.3) rotate(-15deg);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.15) rotate(5deg);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    @keyframes elegantFloat {
        0%, 100% {
            transform: translateY(0) rotateZ(0deg);
        }
        25% {
            transform: translateY(-12px) rotateZ(1deg);
        }
        50% {
            transform: translateY(0) rotateZ(0deg);
        }
        75% {
            transform: translateY(12px) rotateZ(-1deg);
        }
    }

    @keyframes elegantGlow {
        0% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            filter: brightness(1) saturate(1);
        }
        100% {
            box-shadow:
                0 0 40px rgba(147, 51, 234, 0.6),
                0 0 80px rgba(59, 130, 246, 0.4),
                0 0 120px rgba(147, 51, 234, 0.2);
            filter: brightness(1.2) saturate(1.3);
        }
    }

    @keyframes elegantShimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    @keyframes elegantTypewriter {
        0% { width: 0; border-color: #3B82F6; }
        90% { border-color: #3B82F6; }
        100% { width: 100%; border-color: transparent; }
    }

    @keyframes elegantReveal {
        0% {
            opacity: 0;
            transform: translateY(120px) scale(0.7);
            clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0% 100%);
        }
        50% {
            opacity: 0.8;
            transform: translateY(-20px) scale(1.05);
            clip-path: polygon(0 60%, 100% 60%, 100% 100%, 0% 100%);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
        }
    }

    @keyframes elegantMorph {
        0%, 100% {
            border-radius: 24px;
            transform: scale(1) rotate(0deg);
        }
        25% {
            border-radius: 60px;
            transform: scale(1.02) rotate(1deg);
        }
        50% {
            border-radius: 16px;
            transform: scale(1.05) rotate(0deg);
        }
        75% {
            border-radius: 40px;
            transform: scale(1.02) rotate(-1deg);
        }
    }

    @keyframes elegantPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
            filter: brightness(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.03);
            filter: brightness(1.1);
        }
    }

    @keyframes elegantBounce {
        0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
        }
        40%, 43% {
            transform: translate3d(0, -40px, 0) scale(1.08) rotate(2deg);
        }
        70% {
            transform: translate3d(0, -20px, 0) scale(1.04) rotate(-1deg);
        }
        90% {
            transform: translate3d(0, -8px, 0) scale(1.02) rotate(0.5deg);
        }
    }

    /* Professional Responsive Design System */

    /* Mobile First Approach */
    .container-elegant {
        width: 100%;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    @media (min-width: 640px) {
        .container-elegant { padding: 0 1.5rem; }
    }

    @media (min-width: 768px) {
        .container-elegant { padding: 0 2rem; }
    }

    @media (min-width: 1024px) {
        .container-elegant { padding: 0 2.5rem; }
    }

    @media (min-width: 1280px) {
        .container-elegant { padding: 0 3rem; }
    }

    /* Ultra-Responsive Typography System */
    .text-elegant-display {
        font-size: clamp(1.75rem, 4vw + 1rem, 6rem);
        line-height: clamp(1.1, 1.2, 1.3);
        font-weight: 800;
        letter-spacing: clamp(-0.05em, -0.025em, -0.01em);
        background: linear-gradient(135deg, #1e293b, #3b82f6, #8b5cf6);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradient-shift 8s ease-in-out infinite;
    }

    .text-elegant-title {
        font-size: clamp(1.25rem, 2.5vw + 0.5rem, 3rem);
        line-height: clamp(1.2, 1.3, 1.4);
        font-weight: 700;
        letter-spacing: clamp(-0.02em, -0.015em, -0.01em);
        color: #1e293b;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dark .text-elegant-title {
        color: #f8fafc;
    }

    .text-elegant-subtitle {
        font-size: clamp(0.875rem, 1.5vw + 0.25rem, 1.5rem);
        line-height: clamp(1.4, 1.5, 1.6);
        font-weight: 500;
        opacity: 0.8;
        color: #64748b;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dark .text-elegant-subtitle {
        color: #94a3b8;
    }

    .text-elegant-body {
        font-size: clamp(0.875rem, 1vw + 0.25rem, 1.125rem);
        line-height: clamp(1.5, 1.6, 1.7);
        font-weight: 400;
        color: #475569;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dark .text-elegant-body {
        color: #cbd5e1;
    }

    .text-elegant-caption {
        font-size: clamp(0.75rem, 0.8vw + 0.2rem, 1rem);
        line-height: clamp(1.3, 1.4, 1.5);
        font-weight: 500;
        color: #64748b;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dark .text-elegant-caption {
        color: #94a3b8;
    }

    @keyframes gradient-shift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    /* Responsive Spacing */
    .spacing-elegant-xs { padding: 0.5rem; }
    .spacing-elegant-sm { padding: 0.75rem; }
    .spacing-elegant-md { padding: 1rem; }
    .spacing-elegant-lg { padding: 1.5rem; }
    .spacing-elegant-xl { padding: 2rem; }

    @media (min-width: 640px) {
        .spacing-elegant-xs { padding: 0.75rem; }
        .spacing-elegant-sm { padding: 1rem; }
        .spacing-elegant-md { padding: 1.5rem; }
        .spacing-elegant-lg { padding: 2rem; }
        .spacing-elegant-xl { padding: 3rem; }
    }

    @media (min-width: 768px) {
        .spacing-elegant-xs { padding: 1rem; }
        .spacing-elegant-sm { padding: 1.5rem; }
        .spacing-elegant-md { padding: 2rem; }
        .spacing-elegant-lg { padding: 3rem; }
        .spacing-elegant-xl { padding: 4rem; }
    }

    @media (min-width: 1024px) {
        .spacing-elegant-xs { padding: 1.25rem; }
        .spacing-elegant-sm { padding: 2rem; }
        .spacing-elegant-md { padding: 2.5rem; }
        .spacing-elegant-lg { padding: 4rem; }
        .spacing-elegant-xl { padding: 5rem; }
    }

    /* Ultra-Responsive Card System */
    .card-elegant {
        background: rgba(255, 255, 255, 0.95);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: clamp(16px, 3vw + 4px, 32px);
        padding: clamp(1rem, 3vw + 0.5rem, 2.5rem);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.08),
            0 4px 16px rgba(0, 0, 0, 0.04),
            0 1px 4px rgba(0, 0, 0, 0.02),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        overflow: hidden;
    }

    .dark .card-elegant {
        background: rgba(15, 23, 42, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.08);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.4),
            0 4px 16px rgba(0, 0, 0, 0.3),
            0 1px 4px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .card-elegant::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.08) 0%,
            rgba(147, 51, 234, 0.04) 30%,
            rgba(236, 72, 153, 0.06) 60%,
            rgba(59, 130, 246, 0.08) 100%);
        background-size: 200% 200%;
        opacity: 0;
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        pointer-events: none;
        animation: gradient-shift 8s ease-in-out infinite;
    }

    .card-elegant::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1px;
        background: linear-gradient(135deg,
            rgba(255,255,255,0.2) 0%,
            rgba(255,255,255,0.05) 50%,
            rgba(255,255,255,0.1) 100%);
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .card-elegant:hover::before {
        opacity: 1;
        background-position: 100% 100%;
    }

    .card-elegant:hover::after {
        opacity: 1;
    }

    .card-elegant:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 10px 30px rgba(0, 0, 0, 0.1),
            0 4px 12px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .dark .card-elegant:hover {
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.6),
            0 10px 30px rgba(0, 0, 0, 0.4),
            0 4px 12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15);
    }

    /* Card Variants */
    .card-elegant-compact {
        padding: clamp(0.75rem, 2vw + 0.25rem, 1.5rem);
        border-radius: clamp(12px, 2vw + 2px, 20px);
    }

    .card-elegant-spacious {
        padding: clamp(1.5rem, 4vw + 1rem, 3.5rem);
        border-radius: clamp(20px, 4vw + 8px, 40px);
    }

    .card-elegant-flat {
        box-shadow:
            0 1px 3px rgba(0, 0, 0, 0.1),
            0 1px 2px rgba(0, 0, 0, 0.06);
    }

    .card-elegant-elevated {
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 12px 25px rgba(0, 0, 0, 0.1),
            0 6px 12px rgba(0, 0, 0, 0.05);
    }

    /* Ultra-Responsive Button System */
    .btn-elegant-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
        background-size: 200% 200%;
        color: white;
        font-weight: 600;
        font-size: clamp(0.875rem, 1vw + 0.25rem, 1.125rem);
        padding: clamp(0.75rem, 1.5vw + 0.25rem, 1.25rem) clamp(1.5rem, 3vw + 0.5rem, 2.5rem);
        border-radius: clamp(12px, 2vw + 4px, 24px);
        border: none;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        overflow: hidden;
        box-shadow:
            0 8px 32px rgba(59, 130, 246, 0.3),
            0 4px 16px rgba(139, 92, 246, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: clamp(0.375rem, 0.5vw + 0.125rem, 0.75rem);
        min-height: clamp(44px, 6vw + 8px, 56px);
        animation: gradient-shift 6s ease-in-out infinite;
    }

    .btn-elegant-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .btn-elegant-primary::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1px;
        background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask-composite: xor;
        pointer-events: none;
    }

    .btn-elegant-primary:hover::before {
        left: 100%;
    }

    .btn-elegant-primary:hover {
        transform: translateY(-6px) scale(1.03);
        box-shadow:
            0 20px 50px rgba(59, 130, 246, 0.4),
            0 10px 25px rgba(139, 92, 246, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1);
        background-position: 100% 50%;
    }

    .btn-elegant-primary:active {
        transform: translateY(-2px) scale(1.01);
        transition: all 0.1s cubic-bezier(0.23, 1, 0.32, 1);
    }

    .btn-elegant-secondary {
        background: rgba(255, 255, 255, 0.08);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        color: inherit;
        font-weight: 600;
        font-size: clamp(0.875rem, 1vw + 0.25rem, 1.125rem);
        padding: clamp(0.75rem, 1.5vw + 0.25rem, 1.25rem) clamp(1.5rem, 3vw + 0.5rem, 2.5rem);
        border-radius: clamp(12px, 2vw + 4px, 24px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: clamp(0.375rem, 0.5vw + 0.125rem, 0.75rem);
        min-height: clamp(44px, 6vw + 8px, 56px);
        position: relative;
        overflow: hidden;
    }

    .btn-elegant-secondary::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        opacity: 0;
        transition: opacity 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        border-radius: inherit;
    }

    .btn-elegant-secondary:hover::before {
        opacity: 1;
    }

    .btn-elegant-secondary:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-6px) scale(1.03);
        box-shadow:
            0 20px 50px rgba(0, 0, 0, 0.15),
            0 10px 25px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.25);
    }

    .btn-elegant-secondary:active {
        transform: translateY(-2px) scale(1.01);
        transition: all 0.1s cubic-bezier(0.23, 1, 0.32, 1);
    }

    /* Button Variants */
    .btn-elegant-small {
        font-size: clamp(0.75rem, 0.8vw + 0.2rem, 1rem);
        padding: clamp(0.5rem, 1vw + 0.125rem, 0.875rem) clamp(1rem, 2vw + 0.25rem, 1.75rem);
        min-height: clamp(36px, 5vw + 4px, 44px);
    }

    .btn-elegant-large {
        font-size: clamp(1rem, 1.2vw + 0.3rem, 1.375rem);
        padding: clamp(1rem, 2vw + 0.5rem, 1.5rem) clamp(2rem, 4vw + 1rem, 3rem);
        min-height: clamp(52px, 7vw + 12px, 68px);
    }

    .btn-elegant-icon-only {
        padding: clamp(0.75rem, 1.5vw + 0.25rem, 1.25rem);
        aspect-ratio: 1;
        border-radius: 50%;
    }

    /* Mobile Optimizations */
    @media (max-width: 639px) {
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in,
        .animate-elegant-scale,
        .animate-elegant-reveal {
            animation-duration: 0.8s;
        }

        .hover-elegant-lift:hover,
        .hover-elegant-glow:hover,
        .hover-elegant-tilt:hover {
            transform: scale(1.02);
        }

        .card-elegant {
            border-radius: 16px;
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            width: 100%;
            justify-content: center;
        }
    }

    /* Touch Device Optimizations */
    @media (hover: none) and (pointer: coarse) {
        .hover-elegant-lift:hover,
        .hover-elegant-glow:hover,
        .hover-elegant-tilt:hover,
        .hover-elegant-magnetic:hover {
            transform: none;
            box-shadow: inherit;
        }

        .hover-elegant-lift:active,
        .hover-elegant-glow:active,
        .hover-elegant-tilt:active {
            transform: scale(0.98);
        }
    }

    /* High DPI Display Optimizations */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .card-elegant {
            border-width: 0.5px;
        }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in,
        .animate-elegant-scale,
        .animate-elegant-float,
        .animate-elegant-glow,
        .animate-elegant-shimmer,
        .animate-elegant-reveal,
        .animate-elegant-morph,
        .animate-elegant-pulse,
        .animate-elegant-bounce {
            animation: none;
        }

        .hover-elegant-lift,
        .hover-elegant-glow,
        .hover-elegant-tilt,
        .hover-elegant-magnetic,
        .hover-elegant-ripple {
            transition: none;
        }
    }

    /* Ultra-Mobile Optimizations (320px - 480px) */
    @media (max-width: 480px) {
        .container-elegant {
            padding: 0 clamp(0.75rem, 2vw, 1rem);
            max-width: 100%;
        }

        .card-elegant {
            margin-bottom: clamp(0.75rem, 2vw, 1rem);
            border-radius: clamp(12px, 3vw, 16px);
            padding: clamp(1rem, 4vw, 1.5rem);
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            width: 100%;
            justify-content: center;
            font-size: clamp(0.875rem, 2.5vw, 1rem);
            padding: clamp(0.875rem, 3vw, 1rem) clamp(1.25rem, 4vw, 1.5rem);
            border-radius: clamp(10px, 2.5vw, 14px);
        }

        .btn-elegant-small {
            padding: clamp(0.625rem, 2.5vw, 0.75rem) clamp(1rem, 3vw, 1.25rem);
            font-size: clamp(0.75rem, 2vw, 0.875rem);
        }

        /* Mobile-specific animations */
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in {
            animation-duration: 0.6s;
        }

        .hover-elegant-lift:hover,
        .hover-elegant-glow:hover {
            transform: scale(1.01);
        }

        /* Touch-friendly spacing */
        .spacing-elegant-xs { padding: clamp(0.5rem, 2vw, 0.75rem); }
        .spacing-elegant-sm { padding: clamp(0.75rem, 3vw, 1rem); }
        .spacing-elegant-md { padding: clamp(1rem, 4vw, 1.25rem); }
        .spacing-elegant-lg { padding: clamp(1.25rem, 5vw, 1.5rem); }
        .spacing-elegant-xl { padding: clamp(1.5rem, 6vw, 2rem); }
    }

    /* Small Mobile Optimizations (481px - 640px) */
    @media (min-width: 481px) and (max-width: 640px) {
        .container-elegant {
            padding: 0 clamp(1rem, 2.5vw, 1.5rem);
        }

        .card-elegant {
            padding: clamp(1.25rem, 3.5vw, 1.75rem);
            border-radius: clamp(16px, 3vw, 20px);
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            min-width: clamp(120px, 25vw, 160px);
            padding: clamp(0.875rem, 2.5vw, 1.125rem) clamp(1.5rem, 4vw, 2rem);
        }
    }

    /* Tablet Optimizations */
    @media (min-width: 481px) and (max-width: 768px) {
        .container-elegant {
            padding: 0 1.25rem;
        }

        .text-elegant-display {
            font-size: 2.25rem;
        }

        .text-elegant-title {
            font-size: 1.5rem;
        }

        .spacing-elegant-md { padding: 1.5rem; }
        .spacing-elegant-lg { padding: 2rem; }
        .spacing-elegant-xl { padding: 2.5rem; }
    }

    /* Desktop Enhancements */
    @media (min-width: 1024px) {
        .hover-elegant-lift:hover {
            transform: translateY(-20px) rotateX(10deg) rotateY(6deg) scale(1.04);
        }

        .hover-elegant-glow:hover {
            box-shadow:
                0 0 50px rgba(59, 130, 246, 0.5),
                0 0 100px rgba(147, 51, 234, 0.3),
                0 0 150px rgba(59, 130, 246, 0.2);
        }

        .card-elegant:hover {
            transform: translateY(-8px) scale(1.02);
        }
    }

    /* Ultra-wide Screen Optimizations */
    @media (min-width: 1440px) {
        .container-elegant {
            max-width: 1600px;
            padding: 0 4rem;
        }

        .text-elegant-display {
            font-size: 6rem;
        }

        .text-elegant-title {
            font-size: 3rem;
        }

        .spacing-elegant-xl { padding: 6rem; }
    }

    /* Print Styles */
    @media print {
        .animate-elegant-entrance,
        .animate-elegant-slide-up,
        .animate-elegant-fade-in,
        .animate-elegant-scale,
        .animate-elegant-float,
        .animate-elegant-glow,
        .animate-elegant-shimmer,
        .animate-elegant-reveal,
        .animate-elegant-morph,
        .animate-elegant-pulse,
        .animate-elegant-bounce {
            animation: none;
        }

        .card-elegant {
            box-shadow: none;
            border: 1px solid #e5e7eb;
        }

        .btn-elegant-primary,
        .btn-elegant-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
    }

    /* High Contrast Mode */
    @media (prefers-contrast: high) {
        .card-elegant {
            border: 2px solid;
            background: white;
        }

        .dark .card-elegant {
            background: black;
            border-color: white;
        }

        .btn-elegant-primary {
            background: black;
            color: white;
            border: 2px solid white;
        }

        .btn-elegant-secondary {
            background: white;
            color: black;
            border: 2px solid black;
        }
    }

    /* Focus Visible Enhancements */
    .btn-elegant-primary:focus-visible,
    .btn-elegant-secondary:focus-visible,
    .card-elegant:focus-visible {
        outline: 3px solid #3B82F6;
        outline-offset: 2px;
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
    }

    .dark .loading-skeleton {
        background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
        background-size: 200% 100%;
    }

    @keyframes loading-shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    /* Scroll Snap */
    .scroll-snap-container {
        scroll-snap-type: y mandatory;
        overflow-y: scroll;
    }

    .scroll-snap-item {
        scroll-snap-align: start;
    }

    /* Custom Scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #3B82F6, #8B5CF6);
        border-radius: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #2563EB, #7C3AED);
    }
}
