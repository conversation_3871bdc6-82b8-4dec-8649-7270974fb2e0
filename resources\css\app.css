@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import 'aos/dist/aos.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Inter', sans-serif;
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100 dark:bg-gray-800;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-400 dark:bg-gray-600 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-500 dark:bg-gray-500;
    }
}

@layer components {
    /* Glass morphism components */
    .glass {
        -webkit-backdrop-filter: blur(16px);
        backdrop-filter: blur(16px);
    }

    .glass-card {
        @apply backdrop-blur-md bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 rounded-xl;
    }

    .glass-button {
        @apply glass backdrop-blur-md bg-white/20 dark:bg-black/20 border border-white/30 dark:border-white/20 hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-300;
    }

    /* Neumorphism components */
    .neuro-card {
        @apply bg-gray-100 dark:bg-gray-800 rounded-2xl shadow-neumorphism dark:shadow-none;
    }

    .neuro-button {
        @apply bg-gray-100 dark:bg-gray-800 rounded-xl shadow-neumorphism dark:shadow-none hover:shadow-neumorphism-inset dark:hover:shadow-none transition-all duration-300;
    }

    /* Gradient backgrounds */
    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-bg-2 {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .gradient-bg-3 {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .gradient-bg-4 {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    /* Animated gradients */
    .animated-gradient {
        background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
    }

    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Button styles */
    .btn-primary {
        @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    .btn-secondary {
        @apply bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    .btn-accent {
        @apply bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
    }

    /* Card styles */
    .card {
        @apply bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-200 dark:border-gray-700;
    }

    /* Line clamp utility */
    .line-clamp-2 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    .card-glass {
        @apply glass-card hover:bg-white/20 dark:hover:bg-black/20 transform hover:scale-105 transition-all duration-300;
    }

    /* Input styles */
    .input-modern {
        @apply w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-300;
    }

    /* Loading animations */
    .skeleton {
        @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
    }

    .loading-dots {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 80px;
    }

    .loading-dots div {
        position: absolute;
        top: 33px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: #3b82f6;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }

    .loading-dots div:nth-child(1) {
        left: 8px;
        animation: loading-dots1 0.6s infinite;
    }

    .loading-dots div:nth-child(2) {
        left: 8px;
        animation: loading-dots2 0.6s infinite;
    }

    .loading-dots div:nth-child(3) {
        left: 32px;
        animation: loading-dots2 0.6s infinite;
    }

    .loading-dots div:nth-child(4) {
        left: 56px;
        animation: loading-dots3 0.6s infinite;
    }

    @keyframes loading-dots1 {
        0% { transform: scale(0); }
        100% { transform: scale(1); }
    }

    @keyframes loading-dots3 {
        0% { transform: scale(1); }
        100% { transform: scale(0); }
    }

    @keyframes loading-dots2 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(24px, 0); }
    }
}
