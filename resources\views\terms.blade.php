<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="darkMode()" :class="{ 'dark': dark }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Terms of Service - Dream Destinations</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                                </svg>
                            </div>
                            <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Dream Destinations
                            </span>
                        </a>
                    </div>
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 font-medium transition-colors">
                            Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Terms of Service</h1>
                
                <div class="prose prose-lg dark:prose-invert max-w-none">
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        <strong>Last updated:</strong> {{ date('F d, Y') }}
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Acceptance of Terms</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        By accessing and using Dream Destinations, you accept and agree to be bound by the terms and provision of this agreement.
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Use License</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Permission is granted to temporarily use Dream Destinations for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
                    </p>
                    <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 mb-6">
                        <li>Modify or copy the materials</li>
                        <li>Use the materials for any commercial purpose or for any public display</li>
                        <li>Attempt to reverse engineer any software contained on the website</li>
                        <li>Remove any copyright or other proprietary notations from the materials</li>
                    </ul>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">User Content</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        You retain ownership of any content you submit, post or display on or through Dream Destinations. By submitting content, you grant us a worldwide, non-exclusive, royalty-free license to use, copy, reproduce, process, adapt, modify, publish, transmit, display and distribute such content.
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Privacy Policy</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Disclaimer</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        The materials on Dream Destinations are provided on an 'as is' basis. Dream Destinations makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.
                    </p>

                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">Contact Information</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        If you have any questions about these Terms of Service, please contact us at: 
                        <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
