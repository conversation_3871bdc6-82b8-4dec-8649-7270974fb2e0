<?php

namespace App\Http\Controllers;

use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class DestinationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Auth::user()->destinations();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by musim
        if ($request->filled('musim')) {
            $query->byMusim($request->musim);
        }

        // Filter by mood
        if ($request->filled('mood')) {
            $query->byMood($request->mood);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status === 'visited' ? 1 : 0);
        }

        // Sorting
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        $allowedSorts = ['created_at', 'nama_tempat', 'negara', 'musim', 'mood', 'status'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $destinations = $query->paginate(12)->withQueryString();

        return view('destinations.index', compact('destinations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('destinations.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama_tempat' => 'required|string|max:255',
            'negara' => 'required|string|max:255',
            'musim' => ['required', Rule::in(Destination::MUSIM_OPTIONS)],
            'mood' => ['required', Rule::in(Destination::MOOD_OPTIONS)],
            'status' => 'required|boolean',
            'deskripsi' => 'required|string|min:10',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['user_id'] = Auth::id();

        // Handle image upload
        if ($request->hasFile('gambar')) {
            $image = $request->file('gambar');
            $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();

            // Create directory if it doesn't exist
            if (!Storage::disk('public')->exists('destinasi')) {
                Storage::disk('public')->makeDirectory('destinasi');
            }

            $image->storeAs('destinasi', $imageName, 'public');
            $validated['gambar'] = $imageName;
        }

        $destination = Destination::create($validated);

        return redirect()->route('destinations.show', $destination)
            ->with('success', 'Destinasi berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Destination $destination)
    {
        // Ensure user can only view their own destinations
        if ($destination->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to destination.');
        }

        return view('destinations.show', compact('destination'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Destination $destination)
    {
        // Ensure user can only edit their own destinations
        if ($destination->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to destination.');
        }

        return view('destinations.edit', compact('destination'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Destination $destination)
    {
        // Ensure user can only update their own destinations
        if ($destination->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to destination.');
        }

        $validated = $request->validate([
            'nama_tempat' => 'required|string|max:255',
            'negara' => 'required|string|max:255',
            'musim' => ['required', Rule::in(Destination::MUSIM_OPTIONS)],
            'mood' => ['required', Rule::in(Destination::MOOD_OPTIONS)],
            'status' => 'required|boolean',
            'deskripsi' => 'required|string|min:10',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('gambar')) {
            // Delete old image if exists
            if ($destination->gambar && Storage::disk('public')->exists('destinasi/' . $destination->gambar)) {
                Storage::disk('public')->delete('destinasi/' . $destination->gambar);
            }

            $image = $request->file('gambar');
            $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();

            // Create directory if it doesn't exist
            if (!Storage::disk('public')->exists('destinasi')) {
                Storage::disk('public')->makeDirectory('destinasi');
            }

            $image->storeAs('destinasi', $imageName, 'public');
            $validated['gambar'] = $imageName;
        }

        $destination->update($validated);

        return redirect()->route('destinations.show', $destination)
            ->with('success', 'Destinasi berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Destination $destination)
    {
        // Ensure user can only delete their own destinations
        if ($destination->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to destination.');
        }

        // Delete image if exists
        if ($destination->gambar && Storage::disk('public')->exists('destinasi/' . $destination->gambar)) {
            Storage::disk('public')->delete('destinasi/' . $destination->gambar);
        }

        $destination->delete();

        return redirect()->route('destinations.index')
            ->with('success', 'Destinasi berhasil dihapus!');
    }
}
