<?php

// Download Morocco and Raja Ampat with alternative URLs
$destinations = [
    [
        'name' => 'morocco',
        'url' => 'https://images.unsplash.com/photo-1489749798305-4fea3ae436d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'morocco.jpg'
    ],
    [
        'name' => 'raja-ampat-indonesia',
        'url' => 'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1280&q=95',
        'filename' => 'raja-ampat-indonesia.jpg'
    ]
];

$downloadDir = 'public/images/destinations/';

echo "Downloading Morocco and Raja Ampat...\n";

foreach ($destinations as $destination) {
    $filePath = $downloadDir . $destination['filename'];
    
    if (file_exists($filePath)) {
        echo "Skipping {$destination['name']} (already exists)\n";
        continue;
    }
    
    echo "Downloading {$destination['name']}... ";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $destination['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $imageData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($imageData !== false && $httpCode == 200) {
        file_put_contents($filePath, $imageData);
        $fileSize = filesize($filePath);
        echo "✓ Downloaded ({$destination['filename']}) - " . round($fileSize/1024) . "KB\n";
    } else {
        echo "✗ Failed to download (HTTP: $httpCode)\n";
    }
    
    sleep(2);
}

echo "\nDownload completed!\n";

?>
