<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900 dark:text-white leading-tight">
                    <?php echo e($destination->nama_tempat); ?>

                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    <?php echo e($destination->negara); ?> • Added <?php echo e($destination->created_at->diffForHumans()); ?>

                </p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('destinations.edit', $destination)); ?>" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                    </svg>
                    Edit
                </a>
                <a href="<?php echo e(route('destinations.index')); ?>" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Hero Image -->
                    <div class="card-glass overflow-hidden" data-aos="fade-up">
                        <div class="aspect-w-16 aspect-h-9 relative">
                            <img src="<?php echo e($destination->image_url); ?>" 
                                 alt="<?php echo e($destination->nama_tempat); ?>"
                                 class="w-full h-96 object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                            <div class="absolute bottom-6 left-6 right-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h1 class="text-3xl font-bold text-white mb-2"><?php echo e($destination->nama_tempat); ?></h1>
                                        <p class="text-white/90 text-lg"><?php echo e($destination->negara); ?></p>
                                    </div>
                                    <span class="px-4 py-2 text-sm font-semibold rounded-full <?php echo e($destination->status_badge_class); ?>">
                                        <?php echo e($destination->status_text); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="card-glass p-8" data-aos="fade-up" data-aos-delay="100">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">About This Destination</h2>
                        <div class="prose prose-lg dark:prose-invert max-w-none">
                            <p class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line"><?php echo e($destination->deskripsi); ?></p>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Actions</h3>
                        <div class="flex flex-wrap gap-4">
                            <a href="<?php echo e(route('destinations.edit', $destination)); ?>" class="btn-primary">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                </svg>
                                Edit Destination
                            </a>
                            
                            <button onclick="shareDestination()" class="btn-secondary">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"/>
                                </svg>
                                Share
                            </button>

                            <form action="<?php echo e(route('destinations.destroy', $destination)); ?>" 
                                  method="POST" 
                                  class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this destination? This action cannot be undone.')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn-secondary text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"/>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1 space-y-6">
                    <!-- Quick Info -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Quick Info</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Status</span>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full <?php echo e($destination->status_badge_class); ?>">
                                    <?php echo e($destination->status_text); ?>

                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Best Season</span>
                                <span class="px-3 py-1 text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded-full">
                                    <?php echo e(ucfirst($destination->musim)); ?>

                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Travel Mood</span>
                                <span class="px-3 py-1 text-sm font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 rounded-full">
                                    <?php echo e(ucfirst($destination->mood)); ?>

                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Added</span>
                                <span class="text-gray-900 dark:text-white font-medium">
                                    <?php echo e($destination->created_at->format('M j, Y')); ?>

                                </span>
                            </div>
                            
                            <?php if($destination->updated_at != $destination->created_at): ?>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Last Updated</span>
                                <span class="text-gray-900 dark:text-white font-medium">
                                    <?php echo e($destination->updated_at->format('M j, Y')); ?>

                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Season Info -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="400">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Season Guide</h3>
                        <div class="space-y-3">
                            <?php
                                $seasonInfo = [
                                    'spring' => ['icon' => '🌸', 'desc' => 'Mild weather, blooming flowers'],
                                    'summer' => ['icon' => '☀️', 'desc' => 'Warm weather, long days'],
                                    'autumn' => ['icon' => '🍂', 'desc' => 'Cool weather, beautiful colors'],
                                    'winter' => ['icon' => '❄️', 'desc' => 'Cold weather, winter activities']
                                ];
                            ?>
                            
                            <?php $__currentLoopData = $seasonInfo; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $season => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center p-3 rounded-lg <?php echo e($destination->musim == $season ? 'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800' : 'bg-gray-50 dark:bg-gray-700/50'); ?>">
                                <span class="text-2xl mr-3"><?php echo e($info['icon']); ?></span>
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?php echo e(ucfirst($season)); ?></div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($info['desc']); ?></div>
                                </div>
                                <?php if($destination->musim == $season): ?>
                                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Mood Info -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="500">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Travel Mood</h3>
                        <div class="space-y-3">
                            <?php
                                $moodInfo = [
                                    'happy' => ['icon' => '😊', 'desc' => 'Joyful and uplifting experiences'],
                                    'healing' => ['icon' => '🧘', 'desc' => 'Peaceful and restorative'],
                                    'romantic' => ['icon' => '💕', 'desc' => 'Perfect for couples'],
                                    'adventure' => ['icon' => '🏔️', 'desc' => 'Thrilling and exciting'],
                                    'peaceful' => ['icon' => '🕊️', 'desc' => 'Calm and tranquil'],
                                    'exciting' => ['icon' => '🎉', 'desc' => 'Fun and energetic'],
                                    'cultural' => ['icon' => '🏛️', 'desc' => 'Rich in history and culture'],
                                    'spiritual' => ['icon' => '🙏', 'desc' => 'Meaningful and reflective']
                                ];
                            ?>
                            
                            <?php if(isset($moodInfo[$destination->mood])): ?>
                            <div class="flex items-center p-4 rounded-lg bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
                                <span class="text-3xl mr-4"><?php echo e($moodInfo[$destination->mood]['icon']); ?></span>
                                <div>
                                    <div class="font-bold text-purple-900 dark:text-purple-300"><?php echo e(ucfirst($destination->mood)); ?></div>
                                    <div class="text-sm text-purple-700 dark:text-purple-400"><?php echo e($moodInfo[$destination->mood]['desc']); ?></div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Related Actions -->
                    <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="600">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Related</h3>
                        <div class="space-y-3">
                            <a href="<?php echo e(route('destinations.index', ['musim' => $destination->musim])); ?>" 
                               class="block p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-white">More <?php echo e(ucfirst($destination->musim)); ?> Destinations</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">Explore similar seasonal destinations</div>
                                    </div>
                                </div>
                            </a>
                            
                            <a href="<?php echo e(route('destinations.index', ['mood' => $destination->mood])); ?>" 
                               class="block p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-white">More <?php echo e(ucfirst($destination->mood)); ?> Destinations</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">Find destinations with similar vibes</div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function shareDestination() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo e($destination->nama_tempat); ?> - Dream Destinations',
                    text: 'Check out this amazing destination: <?php echo e($destination->nama_tempat); ?>, <?php echo e($destination->negara); ?>',
                    url: window.location.href
                });
            } else {
                // Fallback to copying URL
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                });
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\project_uas_abduls\resources\views/destinations/show.blade.php ENDPATH**/ ?>