<x-app-layout>
    <x-slot name="header">
        <div class="container-fluid">
            <div class="flex-container between responsive">
                <div class="spacing-md">
                    <h1 class="section-title animate-fade-in">
                        {{ __('messages.welcome_back') }}, {{ Auth::user()->name }}! 👋
                    </h1>
                    <p class="body-text animate-slide-up" style="animation-delay: 0.2s;">
                        {{ app()->getLocale() == 'id' ? 'Siap menjelajahi destinasi impian Anda?' : 'Ready to explore your dream destinations?' }}
                    </p>
                </div>
                <div class="flex-container responsive animate-scale-in" style="animation-delay: 0.4s;">
                    <a href="{{ route('destinations.create') }}" class="btn-primary animate-glow">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        {{ __('messages.add_destination') }}
                    </a>
                    <a href="{{ route('destinations.index') }}" class="btn-secondary">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                        </svg>
                        {{ __('messages.view_all') }}
                    </a>
                </div>
            </div>
        </div>
    </x-slot>

    <div class="section-wrapper">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="content-grid cols-4 spacing-lg">
                <!-- Total Destinations -->
                <div class="glass-card animate-slide-left" style="animation-delay: 0.1s;">
                    <div class="flex-container">
                        <div class="p-4 rounded-3xl bg-gradient-to-r from-blue-500 to-blue-600 text-white animate-float">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="caption-text">{{ __('messages.total_destinations') }}</p>
                            <p class="card-title animate-pulse">{{ $totalDestinations }}</p>
                        </div>
                    </div>
                </div>

                <!-- Visited Destinations -->
                <div class="glass-card animate-scale-in" style="animation-delay: 0.2s;">
                    <div class="flex-container">
                        <div class="p-4 rounded-3xl bg-gradient-to-r from-green-500 to-green-600 text-white animate-float" style="animation-delay: 0.5s;">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div>
                            <p class="caption-text">{{ __('messages.visited') }}</p>
                            <p class="card-title animate-pulse">{{ $visitedDestinations }}</p>
                        </div>
                    </div>
                </div>

                <!-- Dream Destinations -->
                <div class="glass-card animate-slide-right" style="animation-delay: 0.3s;">
                    <div class="flex-container">
                        <div class="p-4 rounded-3xl bg-gradient-to-r from-purple-500 to-purple-600 text-white animate-float" style="animation-delay: 1s;">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="caption-text">{{ __('messages.dream_destinations') }}</p>
                            <p class="card-title animate-pulse">{{ $dreamDestinations }}</p>
                        </div>
                    </div>
                </div>

                <!-- Completion Rate -->
                <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $totalDestinations > 0 ? round(($visitedDestinations / $totalDestinations) * 100) : 0 }}%
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            @if($totalDestinations > 0)
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Status Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Destination Status</h3>
                    <div class="relative h-64">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>

                <!-- Season Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Preferred Seasons</h3>
                    <div class="relative h-64">
                        <canvas id="seasonChart"></canvas>
                    </div>
                </div>
            </div>
            @endif

            <!-- Recent Destinations -->
            @if($recentDestinations->count() > 0)
            <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="800">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">Recent Destinations</h3>
                    <a href="{{ route('destinations.index') }}" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                        View All →
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($recentDestinations as $destination)
                    <div class="card overflow-hidden">
                        <div class="aspect-w-16 aspect-h-12 relative">
                            <img src="{{ $destination->image_url }}"
                                 alt="{{ $destination->nama_tempat }}"
                                 class="w-full h-32 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full {{ $destination->status_badge_class }}">
                                    {{ $destination->status_text }}
                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="font-bold text-gray-900 dark:text-white mb-1">{{ $destination->nama_tempat }}</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ $destination->negara }}</p>
                            <div class="flex items-center justify-between text-xs">
                                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded-full">
                                    {{ ucfirst($destination->musim) }}
                                </span>
                                <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 rounded-full">
                                    {{ ucfirst($destination->mood) }}
                                </span>
                            </div>
                            <div class="mt-3 flex space-x-2">
                                <a href="{{ route('destinations.show', $destination) }}" class="flex-1 text-center bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 py-2 px-3 rounded-lg text-sm font-medium hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                                    View
                                </a>
                                <a href="{{ route('destinations.edit', $destination) }}" class="flex-1 text-center bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                    Edit
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @else
            <!-- Empty State -->
            <div class="card-glass p-12 text-center" data-aos="fade-up" data-aos-delay="800">
                <div class="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Start Your Journey</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    You haven't added any destinations yet. Start building your dream travel list!
                </p>
                <a href="{{ route('destinations.create') }}" class="inline-flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    {{ __('messages.add_first_destination') }}
                </a>
            </div>
            @endif
        </div>
    </div>

    <!-- Chart.js Scripts -->
    @if($totalDestinations > 0)
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Status Chart (Doughnut)
            const statusCtx = document.getElementById('statusChart');
            if (statusCtx) {
                new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Visited', 'Dreams'],
                    datasets: [{
                        data: [{{ $visitedDestinations }}, {{ $dreamDestinations }}],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(168, 85, 247, 0.8)'
                        ],
                        borderColor: [
                            'rgba(34, 197, 94, 1)',
                            'rgba(168, 85, 247, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#374151',
                                padding: 20
                            }
                        }
                    }
                }
            });

            }

            // Season Chart (Bar)
            const seasonCtx = document.getElementById('seasonChart');
            if (seasonCtx) {
                new Chart(seasonCtx, {
                type: 'bar',
                data: {
                    labels: ['Spring', 'Summer', 'Autumn', 'Winter'],
                    datasets: [{
                        label: 'Destinations',
                        data: [
                            {{ $musimStats['spring'] ?? 0 }},
                            {{ $musimStats['summer'] ?? 0 }},
                            {{ $musimStats['autumn'] ?? 0 }},
                            {{ $musimStats['winter'] ?? 0 }}
                        ],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(251, 191, 36, 0.8)',
                            'rgba(249, 115, 22, 0.8)',
                            'rgba(59, 130, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(34, 197, 94, 1)',
                            'rgba(251, 191, 36, 1)',
                            'rgba(249, 115, 22, 1)',
                            'rgba(59, 130, 246, 1)'
                        ],
                        borderWidth: 2,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#374151'
                            },
                            grid: {
                                color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                            }
                        },
                        x: {
                            ticks: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#374151'
                            },
                            grid: {
                                color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                            }
                        }
                    }
                }
            });
            }
        });
    </script>
    @endif
</x-app-layout>
