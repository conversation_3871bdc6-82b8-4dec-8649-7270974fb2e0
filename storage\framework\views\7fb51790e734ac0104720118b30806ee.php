<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900 dark:text-white leading-tight">
                    Welcome back, <?php echo e(Auth::user()->name); ?>! 👋
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Ready to explore your dream destinations?
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('destinations.create')); ?>" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Add Destination
                </a>
                <a href="<?php echo e(route('destinations.index')); ?>" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                    </svg>
                    View All
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Destinations -->
                <div class="glass-card-enhanced p-6 hover-lift transition-smooth animate-slide-in-left">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Destinations</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($totalDestinations); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Visited Destinations -->
                <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Visited</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($visitedDestinations); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Dream Destinations -->
                <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Dreams</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($dreamDestinations); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Completion Rate -->
                <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                <?php echo e($totalDestinations > 0 ? round(($visitedDestinations / $totalDestinations) * 100) : 0); ?>%
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <?php if($totalDestinations > 0): ?>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Status Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Destination Status</h3>
                    <div class="relative h-64">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>

                <!-- Season Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Preferred Seasons</h3>
                    <div class="relative h-64">
                        <canvas id="seasonChart"></canvas>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recent Destinations -->
            <?php if($recentDestinations->count() > 0): ?>
            <div class="card-glass p-6" data-aos="fade-up" data-aos-delay="800">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">Recent Destinations</h3>
                    <a href="<?php echo e(route('destinations.index')); ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                        View All →
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $recentDestinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card overflow-hidden">
                        <div class="aspect-w-16 aspect-h-12 relative">
                            <img src="<?php echo e($destination->image_url); ?>"
                                 alt="<?php echo e($destination->nama_tempat); ?>"
                                 class="w-full h-32 object-cover">
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full <?php echo e($destination->status_badge_class); ?>">
                                    <?php echo e($destination->status_text); ?>

                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="font-bold text-gray-900 dark:text-white mb-1"><?php echo e($destination->nama_tempat); ?></h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2"><?php echo e($destination->negara); ?></p>
                            <div class="flex items-center justify-between text-xs">
                                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded-full">
                                    <?php echo e(ucfirst($destination->musim)); ?>

                                </span>
                                <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 rounded-full">
                                    <?php echo e(ucfirst($destination->mood)); ?>

                                </span>
                            </div>
                            <div class="mt-3 flex space-x-2">
                                <a href="<?php echo e(route('destinations.show', $destination)); ?>" class="flex-1 text-center bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 py-2 px-3 rounded-lg text-sm font-medium hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                                    View
                                </a>
                                <a href="<?php echo e(route('destinations.edit', $destination)); ?>" class="flex-1 text-center bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                    Edit
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php else: ?>
            <!-- Empty State -->
            <div class="card-glass p-12 text-center" data-aos="fade-up" data-aos-delay="800">
                <div class="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Start Your Journey</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    You haven't added any destinations yet. Start building your dream travel list!
                </p>
                <a href="<?php echo e(route('destinations.create')); ?>" class="inline-flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo e(__('messages.add_first_destination')); ?>

                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Chart.js Scripts -->
    <?php if($totalDestinations > 0): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Status Chart (Doughnut)
            const statusCtx = document.getElementById('statusChart');
            if (statusCtx) {
                new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Visited', 'Dreams'],
                    datasets: [{
                        data: [<?php echo e($visitedDestinations); ?>, <?php echo e($dreamDestinations); ?>],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(168, 85, 247, 0.8)'
                        ],
                        borderColor: [
                            'rgba(34, 197, 94, 1)',
                            'rgba(168, 85, 247, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#374151',
                                padding: 20
                            }
                        }
                    }
                }
            });

            }

            // Season Chart (Bar)
            const seasonCtx = document.getElementById('seasonChart');
            if (seasonCtx) {
                new Chart(seasonCtx, {
                type: 'bar',
                data: {
                    labels: ['Spring', 'Summer', 'Autumn', 'Winter'],
                    datasets: [{
                        label: 'Destinations',
                        data: [
                            <?php echo e($musimStats['spring'] ?? 0); ?>,
                            <?php echo e($musimStats['summer'] ?? 0); ?>,
                            <?php echo e($musimStats['autumn'] ?? 0); ?>,
                            <?php echo e($musimStats['winter'] ?? 0); ?>

                        ],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(251, 191, 36, 0.8)',
                            'rgba(249, 115, 22, 0.8)',
                            'rgba(59, 130, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(34, 197, 94, 1)',
                            'rgba(251, 191, 36, 1)',
                            'rgba(249, 115, 22, 1)',
                            'rgba(59, 130, 246, 1)'
                        ],
                        borderWidth: 2,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#374151'
                            },
                            grid: {
                                color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                            }
                        },
                        x: {
                            ticks: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#374151'
                            },
                            grid: {
                                color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                            }
                        }
                    }
                }
            });
            }
        });
    </script>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\project_uas_abduls\resources\views/dashboard.blade.php ENDPATH**/ ?>