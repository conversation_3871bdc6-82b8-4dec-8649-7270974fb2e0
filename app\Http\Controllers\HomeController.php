<?php

namespace App\Http\Controllers;

use App\Models\Destination;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get random destinations for public gallery (preview)
        $featuredDestinations = Destination::with('user')
            ->inRandomOrder()
            ->take(12)
            ->get();

        // Get total statistics for landing page
        $totalDestinations = Destination::count() ?: 0;
        $totalUsers = \App\Models\User::count() ?: 0;
        $visitedDestinations = Destination::where('status', true)->count() ?: 0;

        return view('welcome', compact(
            'featuredDestinations',
            'totalDestinations',
            'totalUsers',
            'visitedDestinations'
        ));
    }

    public function gallery(Request $request)
    {
        $query = Destination::with('user');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by musim
        if ($request->filled('musim')) {
            $query->byMusim($request->musim);
        }

        // Filter by mood
        if ($request->filled('mood')) {
            $query->byMood($request->mood);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status === 'visited' ? 1 : 0);
        }

        $destinations = $query->latest()->paginate(24)->withQueryString();

        return view('gallery', compact('destinations'));
    }
}
