<?php

// Download final 2 hero images
$heroImages = [
    [
        'name' => 'hero-green-forest',
        'url' => 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&h=1440&q=95',
        'filename' => 'hero-green-forest.jpg',
        'description' => 'Dense green forest from above'
    ],
    [
        'name' => 'hero-ocean-sunset',
        'url' => 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&h=1440&q=95',
        'filename' => 'hero-ocean-sunset.jpg',
        'description' => 'Sunset over ocean waves'
    ]
];

$downloadDir = 'public/images/hero/';

echo "Downloading final hero images...\n";

foreach ($heroImages as $image) {
    $filePath = $downloadDir . $image['filename'];
    
    if (file_exists($filePath)) {
        echo "Skipping {$image['description']} (already exists)\n";
        continue;
    }
    
    echo "Downloading {$image['description']}... ";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $image['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $imageData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($imageData !== false && $httpCode == 200) {
        file_put_contents($filePath, $imageData);
        $fileSize = filesize($filePath);
        echo "✓ Downloaded ({$image['filename']}) - " . round($fileSize/1024) . "KB\n";
    } else {
        echo "✗ Failed to download (HTTP: $httpCode)\n";
    }
    
    sleep(1);
}

echo "\nDownload completed!\n";

?>
