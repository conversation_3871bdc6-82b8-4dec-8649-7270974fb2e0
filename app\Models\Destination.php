<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Destination extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'nama_tempat',
        'negara',
        'musim',
        'mood',
        'status',
        'deskripsi',
        'gambar',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Constants for enum values
    public const MUSIM_OPTIONS = ['summer', 'winter', 'spring', 'autumn'];
    public const MOOD_OPTIONS = ['happy', 'healing', 'romantic', 'adventure', 'peaceful', 'exciting', 'cultural', 'spiritual'];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    // Accessors
    public function getStatusTextAttribute(): string
    {
        return $this->status ? 'Visited' : 'Dream';
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }

    public function getImageUrlAttribute(): string
    {
        if ($this->gambar && file_exists(storage_path('app/public/destinasi/' . $this->gambar))) {
            return asset('storage/destinasi/' . $this->gambar);
        }

        // Fallback to Unsplash API with destination name
        $query = urlencode($this->nama_tempat . ' ' . $this->negara);
        return "https://source.unsplash.com/800x600/?{$query}";
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByMusim($query, $musim)
    {
        return $query->where('musim', $musim);
    }

    public function scopeByMood($query, $mood)
    {
        return $query->where('mood', $mood);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('nama_tempat', 'like', "%{$search}%")
              ->orWhere('negara', 'like', "%{$search}%")
              ->orWhere('deskripsi', 'like', "%{$search}%");
        });
    }
}
