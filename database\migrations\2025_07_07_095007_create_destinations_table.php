<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('destinations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('nama_tempat');
            $table->string('negara');
            $table->enum('musim', ['summer', 'winter', 'spring', 'autumn']);
            $table->enum('mood', ['happy', 'healing', 'romantic', 'adventure', 'peaceful', 'exciting', 'cultural', 'spiritual']);
            $table->boolean('status')->default(0)->comment('0 = dream, 1 = visited');
            $table->text('deskripsi');
            $table->string('gambar')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'status']);
            $table->index(['musim']);
            $table->index(['mood']);
            $table->index(['negara']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('destinations');
    }
};
