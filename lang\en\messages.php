<?php

return [
    // Navigation
    'dashboard' => 'Dashboard',
    'my_destinations' => 'My Destinations',
    'gallery' => 'Gallery',
    'profile' => 'Profile',
    'logout' => 'Logout',
    'login' => 'Login',
    'register' => 'Register',
    'get_started' => 'Get Started',

    // Hero Section
    'hero_title_1' => 'Manifest',
    'hero_title_2' => 'Dream Vacation',
    'hero_title_3' => 'You',
    'hero_subtitle' => 'Manage your dream destinations, plan unforgettable trips, and make your best life adventures come true with Dream Destinations.',
    'start_now' => 'Start Now',
    'already_have_account' => 'Already have an account?',
    'go_to_dashboard' => 'Go to Dashboard',

    // Statistics
    'registered_destinations' => 'Registered Destinations',
    'active_users' => 'Active Users',
    'dreams_realized' => 'Dreams Realized',

    // Features
    'featured_features' => 'Featured Features',
    'features_subtitle' => 'Manage your dream destinations with advanced features and stunning interface',
    'manage_destinations' => 'Manage Destinations',
    'manage_destinations_desc' => 'Add, edit, and manage your dream destinations easily. Complete with photos, descriptions, and travel details.',
    'interactive_statistics' => 'Interactive Statistics',
    'interactive_statistics_desc' => 'See your travel progress with interesting graphs and statistics. Monitor visited destinations vs those still dreams.',
    'inspiration_gallery' => 'Inspiration Gallery',
    'inspiration_gallery_desc' => 'Explore destination galleries from other users to get inspiration for amazing new trips.',

    // Popular Destinations
    'popular_destinations' => 'Popular Destinations',
    'popular_destinations_subtitle' => 'Find inspiration from other users\' dream destinations',
    'view_all_destinations' => 'View All Destinations',

    // CTA Section
    'ready_to_start' => 'Ready to Start Adventure?',
    'cta_subtitle' => 'Join thousands of users who have realized their vacation dreams with Dream Destinations.',
    'register_free_now' => 'Register Free Now',
    'login_to_account' => 'Login to Account',

    // Dashboard
    'welcome_back' => 'Welcome back',
    'total_destinations' => 'Total Destinations',
    'visited_destinations' => 'Visited Destinations',
    'dream_destinations' => 'Dream Destinations',
    'add_destination' => 'Add Destination',
    'view_all' => 'View All',
    'destination_status' => 'Destination Status',
    'preferred_seasons' => 'Preferred Seasons',
    'recent_destinations' => 'Recent Destinations',
    'add_first_destination' => 'Add Your First Destination',
    'start_planning' => 'Start planning your dream vacation by adding your first destination.',

    // Destinations
    'search' => 'Search',
    'search_destinations' => 'Search destinations...',
    'season' => 'Season',
    'all_seasons' => 'All Seasons',
    'summer' => 'Summer',
    'winter' => 'Winter',
    'spring' => 'Spring',
    'autumn' => 'Autumn',
    'mood' => 'Mood',
    'all_moods' => 'All Moods',
    'happy' => 'Happy',
    'healing' => 'Healing',
    'romantic' => 'Romantic',
    'adventure' => 'Adventure',
    'peaceful' => 'Peaceful',
    'exciting' => 'Exciting',
    'cultural' => 'Cultural',
    'spiritual' => 'Spiritual',
    'status' => 'Status',
    'all_status' => 'All Status',
    'visited' => 'Visited',
    'dream' => 'Dream',
    'clear_filters' => 'Clear Filters',

    // Forms
    'place_name' => 'Place Name',
    'country' => 'Country',
    'description' => 'Description',
    'photo' => 'Photo',
    'choose_photo' => 'Choose Photo',
    'remove' => 'Remove',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'delete' => 'Delete',

    // Profile
    'profile_information' => 'Profile Information',
    'profile_photo' => 'Profile Photo',
    'name' => 'Name',
    'email' => 'Email',
    'password' => 'Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',

    // Auth
    'continue_with_google' => 'Continue with Google',
    'sign_up_with_google' => 'Sign up with Google',
    'forgot_password' => 'Forgot your password?',
    'remember_me' => 'Remember me',

    // Footer
    'footer_text' => '© 2025 Dream Destinations. Abdul Somad Maulana (241351076) - Fakultas Informatika',

    // Language
    'language' => 'Language',
    'english' => 'English',
    'indonesian' => 'Indonesian',
];
