<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900 dark:text-white leading-tight">
                    My Destinations
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Manage your dream destinations and travel memories
                </p>
            </div>
            <a href="{{ route('destinations.create') }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                </svg>
                Add New Destination
            </a>
        </div>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Search and Filters -->
            <div class="card-glass p-6 mb-8" x-data="searchFilter()">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                        <input type="text" 
                               id="search" 
                               name="search"
                               x-model="search"
                               placeholder="Search destinations..."
                               class="input-modern"
                               value="{{ request('search') }}">
                    </div>

                    <!-- Season Filter -->
                    <div>
                        <label for="musim" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Season</label>
                        <select id="musim" name="musim" x-model="musim" class="input-modern">
                            <option value="">All Seasons</option>
                            @foreach(\App\Models\Destination::MUSIM_OPTIONS as $musim)
                                <option value="{{ $musim }}" {{ request('musim') == $musim ? 'selected' : '' }}>
                                    {{ ucfirst($musim) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Mood Filter -->
                    <div>
                        <label for="mood" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Mood</label>
                        <select id="mood" name="mood" x-model="mood" class="input-modern">
                            <option value="">All Moods</option>
                            @foreach(\App\Models\Destination::MOOD_OPTIONS as $mood)
                                <option value="{{ $mood }}" {{ request('mood') == $mood ? 'selected' : '' }}>
                                    {{ ucfirst($mood) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                        <select id="status" name="status" x-model="status" class="input-modern">
                            <option value="">All Status</option>
                            <option value="dream" {{ request('status') == 'dream' ? 'selected' : '' }}>Dream</option>
                            <option value="visited" {{ request('status') == 'visited' ? 'selected' : '' }}>Visited</option>
                        </select>
                    </div>
                </div>

                <!-- Filter Actions -->
                <div class="flex items-center justify-between mt-6">
                    <div class="flex space-x-3">
                        <button onclick="applyFilters()" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                            </svg>
                            Apply Filters
                        </button>
                        <button onclick="clearFilters()" class="btn-secondary">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Clear
                        </button>
                    </div>

                    <!-- Sort Options -->
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort by:</label>
                        <select onchange="applySorting(this.value)" class="input-modern text-sm">
                            <option value="created_at-desc" {{ request('sort') == 'created_at' && request('order') == 'desc' ? 'selected' : '' }}>Newest First</option>
                            <option value="created_at-asc" {{ request('sort') == 'created_at' && request('order') == 'asc' ? 'selected' : '' }}>Oldest First</option>
                            <option value="nama_tempat-asc" {{ request('sort') == 'nama_tempat' && request('order') == 'asc' ? 'selected' : '' }}>Name A-Z</option>
                            <option value="nama_tempat-desc" {{ request('sort') == 'nama_tempat' && request('order') == 'desc' ? 'selected' : '' }}>Name Z-A</option>
                            <option value="negara-asc" {{ request('sort') == 'negara' && request('order') == 'asc' ? 'selected' : '' }}>Country A-Z</option>
                        </select>
                    </div>
                </div>
            </div>

            @if($destinations->count() > 0)
                <!-- Destinations Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                    @foreach($destinations as $destination)
                    <div class="card overflow-hidden card-animate" data-aos="fade-up" data-aos-delay="{{ $loop->index * 50 }}">
                        <div class="aspect-w-16 aspect-h-12 relative overflow-hidden">
                            <img src="{{ $destination->image_url }}" 
                                 alt="{{ $destination->nama_tempat }}"
                                 class="w-full h-48 object-cover transition-transform duration-300 hover:scale-110"
                                 loading="lazy">
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full {{ $destination->status_badge_class }}">
                                    {{ $destination->status_text }}
                                </span>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-4 left-4 right-4">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('destinations.show', $destination) }}" 
                                           class="flex-1 bg-white/90 text-gray-900 text-center py-2 px-3 rounded-lg text-sm font-medium hover:bg-white transition-colors">
                                            View
                                        </a>
                                        <a href="{{ route('destinations.edit', $destination) }}" 
                                           class="flex-1 bg-primary-600/90 text-white text-center py-2 px-3 rounded-lg text-sm font-medium hover:bg-primary-600 transition-colors">
                                            Edit
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-900 dark:text-white mb-1 truncate">{{ $destination->nama_tempat }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 truncate">{{ $destination->negara }}</p>
                            
                            <div class="flex items-center justify-between text-xs mb-3">
                                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded-full">
                                    {{ ucfirst($destination->musim) }}
                                </span>
                                <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 rounded-full">
                                    {{ ucfirst($destination->mood) }}
                                </span>
                            </div>

                            <p class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 mb-3">
                                {{ Str::limit($destination->deskripsi, 80) }}
                            </p>

                            <div class="flex space-x-2">
                                <a href="{{ route('destinations.show', $destination) }}" 
                                   class="flex-1 text-center bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 py-2 px-3 rounded-lg text-sm font-medium hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                                    View Details
                                </a>
                                <a href="{{ route('destinations.edit', $destination) }}" 
                                   class="flex-1 text-center bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                    Edit
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $destinations->links() }}
                </div>
            @else
                <!-- Empty State -->
                <div class="card-glass p-12 text-center">
                    <div class="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
                        @if(request()->hasAny(['search', 'musim', 'mood', 'status']))
                            No destinations found
                        @else
                            No destinations yet
                        @endif
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        @if(request()->hasAny(['search', 'musim', 'mood', 'status']))
                            Try adjusting your search criteria or filters to find what you're looking for.
                        @else
                            Start building your dream travel list by adding your first destination!
                        @endif
                    </p>
                    @if(request()->hasAny(['search', 'musim', 'mood', 'status']))
                        <button onclick="clearFilters()" class="btn-secondary mr-3">
                            Clear Filters
                        </button>
                    @endif
                    <a href="{{ route('destinations.create') }}" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        Add Destination
                    </a>
                </div>
            @endif
        </div>
    </div>

    <script>
        function applyFilters() {
            const search = document.getElementById('search').value;
            const musim = document.getElementById('musim').value;
            const mood = document.getElementById('mood').value;
            const status = document.getElementById('status').value;
            
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (musim) params.append('musim', musim);
            if (mood) params.append('mood', mood);
            if (status) params.append('status', status);
            
            // Preserve current sort
            const currentSort = '{{ request("sort", "created_at") }}';
            const currentOrder = '{{ request("order", "desc") }}';
            params.append('sort', currentSort);
            params.append('order', currentOrder);
            
            window.location.href = '{{ route("destinations.index") }}?' + params.toString();
        }

        function clearFilters() {
            window.location.href = '{{ route("destinations.index") }}';
        }

        function applySorting(value) {
            const [sort, order] = value.split('-');
            const params = new URLSearchParams(window.location.search);
            params.set('sort', sort);
            params.set('order', order);
            window.location.href = '{{ route("destinations.index") }}?' + params.toString();
        }

        // Auto-apply filters on Enter key
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    </script>
</x-app-layout>
