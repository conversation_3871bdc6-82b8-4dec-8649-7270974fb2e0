<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class GoogleController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirect()
    {
        try {
            // Check if Google OAuth is configured
            $config = config('services.google');
            if (empty($config['client_id']) || empty($config['client_secret']) || empty($config['redirect'])) {
                return redirect()->route('login')->with('error', 'Google OAuth is not configured properly. Please contact administrator.');
            }

            return Socialite::driver('google')->redirect();
        } catch (\Exception $e) {
            \Log::error('Google OAuth redirect error: ' . $e->getMessage());
            return redirect()->route('login')->with('error', 'Unable to connect to Google. Please try again or use email login.');
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function callback()
    {
        try {
            // Check if Google OAuth is configured
            $config = config('services.google');
            if (empty($config['client_id']) || empty($config['client_secret']) || empty($config['redirect'])) {
                return redirect()->route('login')->with('error', 'Google OAuth is not configured properly. Please contact administrator.');
            }

            $googleUser = Socialite::driver('google')->user();

            // Check if user already exists with this Google ID
            $user = User::where('google_id', $googleUser->id)->first();

            if ($user) {
                // Update user info if needed
                $user->update([
                    'name' => $googleUser->name,
                    'avatar' => $googleUser->avatar,
                ]);

                Auth::login($user);

                return redirect()->intended('/dashboard')->with('success', 'Welcome back, ' . $user->name . '!');
            }

            // Check if user exists with same email
            $existingUser = User::where('email', $googleUser->email)->first();

            if ($existingUser) {
                // Link Google account to existing user
                $existingUser->update([
                    'google_id' => $googleUser->id,
                    'avatar' => $googleUser->avatar,
                ]);

                Auth::login($existingUser);

                return redirect()->intended('/dashboard')->with('success', 'Google account linked successfully!');
            }

            // Create new user
            $newUser = User::create([
                'name' => $googleUser->name,
                'email' => $googleUser->email,
                'google_id' => $googleUser->id,
                'avatar' => $googleUser->avatar,
                'password' => Hash::make(Str::random(24)), // Random password for security
                'email_verified_at' => now(), // Google accounts are pre-verified
            ]);

            Auth::login($newUser);

            return redirect()->intended('/dashboard')->with('success', 'Welcome to Dream Destinations, ' . $newUser->name . '!');

        } catch (\Exception $e) {
            \Log::error('Google OAuth callback error: ' . $e->getMessage());
            \Log::error('Google OAuth callback trace: ' . $e->getTraceAsString());
            return redirect('/login')->with('error', 'Something went wrong with Google authentication. Please try again.');
        }
    }
}
